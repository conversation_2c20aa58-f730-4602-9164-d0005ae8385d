/**
 * Custom hook for handling async operations with loading states,
 * error handling, and retry functionality
 */

import { useState, useCallback, useRef, useEffect } from 'react';
import { useErrorHandler } from '../utils/errorHandler';
import { isNetworkError } from '../utils/networkUtils';

interface AsyncOperationState<T> {
  data: T | null;
  loading: boolean;
  error: string | null;
  lastUpdated: Date | null;
}

interface AsyncOperationOptions {
  retryAttempts?: number;
  retryDelay?: number;
  showToastOnError?: boolean;
  cacheKey?: string;
  cacheDuration?: number;
}

interface AsyncOperationReturn<T> {
  data: T | null;
  loading: boolean;
  error: string | null;
  lastUpdated: Date | null;
  execute: (...args: any[]) => Promise<T | null>;
  retry: () => Promise<T | null>;
  reset: () => void;
  isStale: boolean;
}

// Simple cache for async operations
const operationCache = new Map<string, { data: any; timestamp: number }>();

export function useAsyncOperation<T = any>(
  asyncFunction: (...args: any[]) => Promise<T>,
  options: AsyncOperationOptions = {}
): AsyncOperationReturn<T> {
  const {
    retryAttempts = 3,
    retryDelay = 1000,
    showToastOnError = true,
    cacheKey,
    cacheDuration = 5 * 60 * 1000 // 5 minutes
  } = options;

  const [state, setState] = useState<AsyncOperationState<T>>({
    data: null,
    loading: false,
    error: null,
    lastUpdated: null
  });

  const { handleError } = useErrorHandler();
  const lastArgsRef = useRef<any[]>([]);
  const abortControllerRef = useRef<AbortController | null>(null);
  const retryCountRef = useRef(0);

  // Check if cached data is stale
  const isStale = state.lastUpdated 
    ? Date.now() - state.lastUpdated.getTime() > cacheDuration
    : true;

  // Get cached data if available and not stale
  const getCachedData = useCallback((): T | null => {
    if (!cacheKey) return null;
    
    const cached = operationCache.get(cacheKey);
    if (!cached) return null;
    
    const isExpired = Date.now() - cached.timestamp > cacheDuration;
    if (isExpired) {
      operationCache.delete(cacheKey);
      return null;
    }
    
    return cached.data;
  }, [cacheKey, cacheDuration]);

  // Set cached data
  const setCachedData = useCallback((data: T) => {
    if (cacheKey) {
      operationCache.set(cacheKey, {
        data,
        timestamp: Date.now()
      });
    }
  }, [cacheKey]);

  // Sleep utility for retry delays
  const sleep = (ms: number): Promise<void> => {
    return new Promise(resolve => setTimeout(resolve, ms));
  };

  // Execute the async operation
  const execute = useCallback(async (...args: any[]): Promise<T | null> => {
    // Cancel any ongoing operation
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }

    // Check cache first
    const cachedData = getCachedData();
    if (cachedData && !isStale) {
      setState(prev => ({
        ...prev,
        data: cachedData,
        loading: false,
        error: null,
        lastUpdated: new Date()
      }));
      return cachedData;
    }

    // Store args for retry
    lastArgsRef.current = args;
    retryCountRef.current = 0;

    // Create new abort controller
    abortControllerRef.current = new AbortController();

    setState(prev => ({
      ...prev,
      loading: true,
      error: null
    }));

    let lastError: any;

    // Retry loop
    for (let attempt = 1; attempt <= retryAttempts; attempt++) {
      try {
        const result = await asyncFunction(...args);
        
        // Cache the result
        setCachedData(result);
        
        setState(prev => ({
          ...prev,
          data: result,
          loading: false,
          error: null,
          lastUpdated: new Date()
        }));

        retryCountRef.current = 0;
        return result;

      } catch (error: any) {
        lastError = error;

        // Don't retry if operation was aborted
        if (error.name === 'AbortError') {
          setState(prev => ({
            ...prev,
            loading: false,
            error: 'Operation cancelled'
          }));
          return null;
        }

        // Don't retry for client errors (4xx)
        if (error.status >= 400 && error.status < 500) {
          break;
        }

        // Wait before retrying (except on last attempt)
        if (attempt < retryAttempts) {
          await sleep(retryDelay * attempt); // Exponential backoff
        }
      }
    }

    // All retries failed
    const errorMessage = isNetworkError(lastError)
      ? 'Network error. Please check your connection and try again.'
      : lastError?.message || 'An unexpected error occurred';

    setState(prev => ({
      ...prev,
      loading: false,
      error: errorMessage
    }));

    // Show toast notification if enabled
    if (showToastOnError) {
      handleError(lastError, errorMessage);
    }

    return null;
  }, [
    asyncFunction,
    retryAttempts,
    retryDelay,
    showToastOnError,
    handleError,
    getCachedData,
    setCachedData,
    isStale
  ]);

  // Retry with last used arguments
  const retry = useCallback(async (): Promise<T | null> => {
    retryCountRef.current++;
    return execute(...lastArgsRef.current);
  }, [execute]);

  // Reset state
  const reset = useCallback(() => {
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }
    
    setState({
      data: null,
      loading: false,
      error: null,
      lastUpdated: null
    });
    
    retryCountRef.current = 0;
    lastArgsRef.current = [];
  }, []);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
    };
  }, []);

  return {
    data: state.data,
    loading: state.loading,
    error: state.error,
    lastUpdated: state.lastUpdated,
    execute,
    retry,
    reset,
    isStale
  };
}

// Specialized hook for data fetching
export function useAsyncData<T = any>(
  fetchFunction: () => Promise<T>,
  dependencies: any[] = [],
  options: AsyncOperationOptions = {}
) {
  const operation = useAsyncOperation(fetchFunction, options);

  // Auto-execute on mount and when dependencies change
  useEffect(() => {
    operation.execute();
  }, dependencies);

  return operation;
}
