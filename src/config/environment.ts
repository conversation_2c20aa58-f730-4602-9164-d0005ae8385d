/**
 * Environment configuration for the application
 */

// Environment types
export type Environment = 'development' | 'production' | 'test';

// Get current environment
export const getEnvironment = (): Environment => {
  if (import.meta.env.PROD) return 'production';
  if (import.meta.env.DEV) return 'development';
  return 'test';
};

// Environment configuration
export const config = {
  // Current environment
  environment: getEnvironment(),

  // API configuration
  api: {
    baseUrl: import.meta.env.VITE_API_BASE_URL || '/api',
    timeout: 30000, // 30 seconds
    retryAttempts: 3,
  },

  // Feature flags
  features: {
    enableAnalytics: import.meta.env.PROD,
    enableErrorTracking: import.meta.env.PROD,
    enablePerformanceMonitoring: import.meta.env.PROD,
    enableDebugMode: import.meta.env.DEV,
  },

  // Cache configuration
  cache: {
    productCacheExpiration: 2 * 60 * 1000, // 2 minutes
    generalCacheExpiration: 5 * 60 * 1000, // 5 minutes
    maxCacheSize: 100, // Maximum number of cached items
  },

  // UI configuration
  ui: {
    toastDuration: 5000, // 5 seconds
    loadingTimeout: 30000, // 30 seconds
    debounceDelay: 300, // 300ms for search
  },

  // Security configuration
  security: {
    tokenExpirationDays: 7,
    maxLoginAttempts: 5,
    lockoutDuration: 15 * 60 * 1000, // 15 minutes
  },

  // Application metadata
  app: {
    name: 'Halocritique',
    version: '1.0.0',
    description: 'Product Review Aggregator',
  }
};

// Utility functions
export const isDevelopment = (): boolean => config.environment === 'development';
export const isProduction = (): boolean => config.environment === 'production';
export const isTest = (): boolean => config.environment === 'test';

// Debug logging (only in development)
export const debugLog = (...args: any[]): void => {
  if (config.features.enableDebugMode) {
    console.log('[DEBUG]', ...args);
  }
};

// Error logging (only in development, or to external service in production)
export const errorLog = (error: any, context?: string): void => {
  if (config.features.enableDebugMode) {
    console.error('[ERROR]', context || 'Unknown context', error);
  }

  // In production, send to error tracking service
  if (config.features.enableErrorTracking) {
    // TODO: Integrate with error tracking service like Sentry
    // Sentry.captureException(error, { extra: { context } });
  }
};

// Performance logging
export const performanceLog = (label: string, duration: number): void => {
  if (config.features.enablePerformanceMonitoring) {
    if (config.features.enableDebugMode) {
      console.log(`[PERFORMANCE] ${label}: ${duration}ms`);
    }

    // In production, send to analytics service
    // TODO: Integrate with analytics service
  }
};

// Validate environment configuration
export const validateConfig = (): boolean => {
  const requiredEnvVars = [];

  // Check for required environment variables in production
  if (isProduction()) {
    // Add any required production environment variables here
    // requiredEnvVars.push('VITE_API_BASE_URL');
  }

  const missingVars = requiredEnvVars.filter(varName => !import.meta.env[varName]);

  if (missingVars.length > 0) {
    console.error('Missing required environment variables:', missingVars);
    return false;
  }

  return true;
};

// Initialize configuration validation
if (!validateConfig()) {
  throw new Error('Invalid environment configuration');
}

export default config;
