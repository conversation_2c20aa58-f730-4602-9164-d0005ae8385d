/**
 * SEO utility functions for generating optimized meta tags and structured data
 */

export interface SEOData {
  title: string;
  description: string;
  keywords: string[];
  canonicalUrl: string;
  ogImage?: string;
  structuredData?: any;
}

/**
 * Generate SEO-optimized title with proper length and keywords
 */
export const generateSEOTitle = (
  productName?: string,
  brandName?: string,
  category?: string,
  pageType: 'product' | 'search' | 'category' | 'brand' | 'home' | 'about' = 'home'
): string => {
  const siteName = 'Halocritique';
  const maxLength = 60;

  let title = '';

  switch (pageType) {
    case 'product':
      title = `${productName}${brandName ? ` by ${brandName}` : ''} - Review & Analysis | ${siteName}`;
      break;
    case 'search':
      title = `${productName} - Product Reviews & Comparisons | ${siteName}`;
      break;
    case 'category':
      title = `${category} Products - Reviews & Comparisons | ${siteName}`;
      break;
    case 'brand':
      title = `${brandName} Products - Reviews & Analysis | ${siteName}`;
      break;
    case 'about':
      title = `About ${siteName} - Honest Product Reviews & Comparisons`;
      break;
    default:
      title = `${siteName} - Honest Product Reviews & Comparisons`;
  }

  // Truncate if too long
  if (title.length > maxLength) {
    title = title.substring(0, maxLength - 3) + '...';
  }

  return title;
};

/**
 * Generate SEO-optimized meta description
 */
export const generateSEODescription = (
  productName?: string,
  brandName?: string,
  category?: string,
  productDescription?: string,
  pageType: 'product' | 'search' | 'category' | 'brand' | 'home' | 'about' = 'home'
): string => {
  const maxLength = 160;
  let description = '';

  switch (pageType) {
    case 'product':
      if (productDescription) {
        description = `${productDescription.substring(0, 100)}... Read our detailed review and analysis of ${productName}${brandName ? ` by ${brandName}` : ''}.`;
      } else {
        description = `Read our detailed review and analysis of ${productName}${brandName ? ` by ${brandName}` : ''}. Expert opinions, pros & cons, and buying recommendations.`;
      }
      break;
    case 'search':
      description = `Find honest reviews and comparisons for ${productName}. Expert analysis and detailed product information to help you make informed purchasing decisions.`;
      break;
    case 'category':
      description = `Browse ${category} product reviews and comparisons. Expert-curated opinions and detailed analysis to help you choose the best ${category?.toLowerCase()} products.`;
      break;
    case 'brand':
      description = `Explore ${brandName} product reviews and comparisons. Find honest opinions and detailed analysis for products from ${brandName}.`;
      break;
    case 'about':
      description = 'Learn about Halocritique\'s mission to provide honest, unbiased product reviews and comparisons. Discover how we help consumers make informed purchasing decisions.';
      break;
    default:
      description = 'Find honest, unbiased product reviews and comparisons. Make informed purchasing decisions with expert-curated opinions and detailed analysis from trusted sources.';
  }

  // Truncate if too long
  if (description.length > maxLength) {
    description = description.substring(0, maxLength - 3) + '...';
  }

  return description;
};

/**
 * Generate SEO keywords array
 */
export const generateSEOKeywords = (
  productName?: string,
  brandName?: string,
  category?: string,
  customKeywords: string[] = [],
  pageType: 'product' | 'search' | 'category' | 'brand' | 'home' | 'about' = 'home'
): string[] => {
  const baseKeywords = ['product reviews', 'product comparisons', 'honest reviews', 'buying guide'];
  const keywords: string[] = [...baseKeywords];

  // Add page-specific keywords
  switch (pageType) {
    case 'product':
      if (productName) {
        keywords.unshift(
          productName.toLowerCase(),
          `${productName.toLowerCase()} review`,
          `${productName.toLowerCase()} analysis`
        );
      }
      if (brandName) {
        keywords.push(brandName.toLowerCase(), `${brandName.toLowerCase()} products`);
      }
      if (category) {
        keywords.push(category.toLowerCase(), `${category.toLowerCase()} reviews`);
      }
      break;
    case 'search':
      if (productName) {
        keywords.unshift(
          productName.toLowerCase(),
          `${productName.toLowerCase()} review`,
          `${productName.toLowerCase()} comparison`
        );
      }
      break;
    case 'category':
      if (category) {
        keywords.unshift(
          category.toLowerCase(),
          `${category.toLowerCase()} reviews`,
          `${category.toLowerCase()} products`,
          `best ${category.toLowerCase()}`
        );
      }
      break;
    case 'brand':
      if (brandName) {
        keywords.unshift(
          brandName.toLowerCase(),
          `${brandName.toLowerCase()} products`,
          `${brandName.toLowerCase()} reviews`
        );
      }
      break;
    case 'about':
      keywords.unshift('about halocritique', 'unbiased reviews', 'review platform');
      break;
  }

  // Add custom keywords
  keywords.push(...customKeywords);

  // Remove duplicates and limit to 15 keywords
  return [...new Set(keywords)].slice(0, 15);
};

/**
 * Generate structured data for products
 */
export const generateProductStructuredData = (product: {
  name: string;
  description: string;
  brand: string;
  category: string;
  image: string;
  score: number;
  price?: number;
  slug: string;
  createdAt?: string;
  updatedAt?: string;
}) => {
  const baseUrl = typeof window !== 'undefined' ? window.location.origin : 'https://halocritique.com';
  
  return {
    '@context': 'https://schema.org',
    '@type': 'Product',
    name: product.name,
    description: product.description,
    brand: {
      '@type': 'Brand',
      name: product.brand
    },
    category: product.category,
    image: product.image.startsWith('http') ? product.image : `${baseUrl}${product.image}`,
    url: `${baseUrl}/products/${product.slug}`,
    aggregateRating: {
      '@type': 'AggregateRating',
      ratingValue: product.score / 20, // Convert percentage to 5-star rating
      bestRating: 5,
      worstRating: 1,
      reviewCount: 1
    },
    offers: product.price ? {
      '@type': 'Offer',
      price: product.price,
      priceCurrency: 'USD',
      availability: 'https://schema.org/InStock'
    } : undefined,
    review: {
      '@type': 'Review',
      reviewRating: {
        '@type': 'Rating',
        ratingValue: product.score / 20,
        bestRating: 5,
        worstRating: 1
      },
      author: {
        '@type': 'Organization',
        name: 'Halocritique'
      },
      datePublished: product.createdAt,
      reviewBody: product.description
    }
  };
};

/**
 * Generate breadcrumb structured data
 */
export const generateBreadcrumbStructuredData = (breadcrumbs: Array<{ name: string; url: string }>) => {
  const baseUrl = typeof window !== 'undefined' ? window.location.origin : 'https://halocritique.com';
  
  return {
    '@context': 'https://schema.org',
    '@type': 'BreadcrumbList',
    itemListElement: breadcrumbs.map((crumb, index) => ({
      '@type': 'ListItem',
      position: index + 1,
      name: crumb.name,
      item: crumb.url.startsWith('http') ? crumb.url : `${baseUrl}${crumb.url}`
    }))
  };
};

/**
 * Validate and clean SEO data
 */
export const validateSEOData = (data: Partial<SEOData>): SEOData => {
  return {
    title: data.title || 'Halocritique - Honest Product Reviews & Comparisons',
    description: data.description || 'Find honest, unbiased product reviews and comparisons.',
    keywords: data.keywords || ['product reviews', 'product comparisons', 'honest reviews'],
    canonicalUrl: data.canonicalUrl || '/',
    ogImage: data.ogImage || '/og-image.jpg',
    structuredData: data.structuredData
  };
};
