/**
 * Network utilities for handling API requests with proper error handling,
 * retry logic, and timeout management
 */

import { createError, ErrorType } from './errorHandler';

// Network configuration
const NETWORK_CONFIG = {
  timeout: 30000, // 30 seconds
  retryAttempts: 3,
  retryDelay: 1000, // 1 second
  retryBackoffMultiplier: 2
};

// Network status checker
export const isOnline = (): boolean => {
  return navigator.onLine;
};

// Check if error is a network error
export const isNetworkError = (error: any): boolean => {
  return (
    error instanceof TypeError && 
    (error.message === 'Failed to fetch' || 
     error.message === 'Network request failed' ||
     error.message.includes('fetch'))
  ) || !isOnline();
};

// Check if error is a timeout error
export const isTimeoutError = (error: any): boolean => {
  return error.name === 'AbortError' || error.message?.includes('timeout');
};

// Sleep utility for retry delays
const sleep = (ms: number): Promise<void> => {
  return new Promise(resolve => setTimeout(resolve, ms));
};

// Enhanced fetch with timeout, retry, and error handling
export const fetchWithRetry = async (
  url: string,
  options: RequestInit = {},
  config: Partial<typeof NETWORK_CONFIG> = {}
): Promise<Response> => {
  const finalConfig = { ...NETWORK_CONFIG, ...config };
  let lastError: any;

  for (let attempt = 1; attempt <= finalConfig.retryAttempts; attempt++) {
    try {
      // Create abort controller for timeout
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), finalConfig.timeout);

      // Make the request
      const response = await fetch(url, {
        ...options,
        signal: controller.signal
      });

      // Clear timeout
      clearTimeout(timeoutId);

      // Check if response is ok
      if (!response.ok) {
        // Don't retry for client errors (4xx)
        if (response.status >= 400 && response.status < 500) {
          throw createError(
            new Error(`HTTP ${response.status}: ${response.statusText}`),
            `Request failed with status ${response.status}`
          );
        }
        
        // Retry for server errors (5xx)
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      return response;

    } catch (error: any) {
      lastError = error;

      // Don't retry for certain types of errors
      if (error.name === 'AbortError') {
        throw createError(error, 'Request timed out. Please try again.');
      }

      if (!isNetworkError(error) && error.status >= 400 && error.status < 500) {
        throw createError(error, 'Request failed. Please check your input and try again.');
      }

      // Log retry attempt (only in development)
      if (import.meta.env.DEV && attempt < finalConfig.retryAttempts) {
        console.log(`Request failed, retrying... (${attempt}/${finalConfig.retryAttempts})`);
      }

      // Wait before retrying (with exponential backoff)
      if (attempt < finalConfig.retryAttempts) {
        const delay = finalConfig.retryDelay * Math.pow(finalConfig.retryBackoffMultiplier, attempt - 1);
        await sleep(delay);
      }
    }
  }

  // All retries failed
  if (isNetworkError(lastError)) {
    throw createError(
      lastError,
      'Network error. Please check your internet connection and try again.'
    );
  }

  throw createError(
    lastError,
    'Service temporarily unavailable. Please try again later.'
  );
};

// Wrapper for JSON API requests
export const apiRequest = async <T = any>(
  url: string,
  options: RequestInit = {},
  config?: Partial<typeof NETWORK_CONFIG>
): Promise<T> => {
  try {
    const response = await fetchWithRetry(url, {
      headers: {
        'Content-Type': 'application/json',
        ...options.headers
      },
      ...options
    }, config);

    // Handle empty responses
    const contentType = response.headers.get('content-type');
    if (!contentType || !contentType.includes('application/json')) {
      return {} as T;
    }

    const data = await response.json();
    return data;

  } catch (error: any) {
    // Re-throw if it's already a properly formatted error
    if (error.type && Object.values(ErrorType).includes(error.type)) {
      throw error;
    }

    // Create a proper error
    throw createError(error, 'Failed to complete request');
  }
};

// Network status monitoring
export class NetworkMonitor {
  private listeners: Array<(isOnline: boolean) => void> = [];

  constructor() {
    window.addEventListener('online', this.handleOnline);
    window.addEventListener('offline', this.handleOffline);
  }

  private handleOnline = () => {
    this.notifyListeners(true);
  };

  private handleOffline = () => {
    this.notifyListeners(false);
  };

  private notifyListeners(isOnline: boolean) {
    this.listeners.forEach(listener => listener(isOnline));
  }

  public addListener(listener: (isOnline: boolean) => void) {
    this.listeners.push(listener);
  }

  public removeListener(listener: (isOnline: boolean) => void) {
    this.listeners = this.listeners.filter(l => l !== listener);
  }

  public destroy() {
    window.removeEventListener('online', this.handleOnline);
    window.removeEventListener('offline', this.handleOffline);
    this.listeners = [];
  }
}

// Global network monitor instance
export const networkMonitor = new NetworkMonitor();
