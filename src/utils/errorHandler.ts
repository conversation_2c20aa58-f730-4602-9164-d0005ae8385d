import { useToast } from '@/components/ui/use-toast';

// Error types
export enum ErrorType {
  NETWORK = 'network',
  AUTHENTICATION = 'authentication',
  AUTHORIZATION = 'authorization',
  VALIDATION = 'validation',
  NOT_FOUND = 'not_found',
  SERVER = 'server',
  UNKNOWN = 'unknown'
}

// Error interface
export interface AppError {
  type: ErrorType;
  message: string;
  details?: string | string[] | Record<string, string>;
  statusCode?: number;
  originalError?: any;
}

// Create a standardized error object
export const createError = (
  error: any,
  defaultMessage = 'An unexpected error occurred'
): AppError => {
  // If it's already an AppError, return it
  if (error && error.type && Object.values(ErrorType).includes(error.type)) {
    return error as AppError;
  }

  // Handle network errors
  if (error instanceof TypeError && error.message === 'Failed to fetch') {
    return {
      type: ErrorType.NETWORK,
      message: 'Network error. Please check your internet connection.',
      originalError: error
    };
  }

  // Handle HTTP errors based on status code
  if (error && error.statusCode) {
    const statusCode = error.statusCode;

    if (statusCode === 401) {
      return {
        type: ErrorType.AUTHENTICATION,
        message: 'Authentication required. Please log in.',
        statusCode,
        originalError: error
      };
    }

    if (statusCode === 403) {
      return {
        type: ErrorType.AUTHORIZATION,
        message: 'You do not have permission to perform this action.',
        statusCode,
        originalError: error
      };
    }

    if (statusCode === 404) {
      return {
        type: ErrorType.NOT_FOUND,
        message: 'The requested resource was not found.',
        statusCode,
        originalError: error
      };
    }

    if (statusCode >= 400 && statusCode < 500) {
      return {
        type: ErrorType.VALIDATION,
        message: error.message || 'Invalid request. Please check your input.',
        details: error.details,
        statusCode,
        originalError: error
      };
    }

    if (statusCode >= 500) {
      return {
        type: ErrorType.SERVER,
        message: 'Server error. Please try again later.',
        statusCode,
        originalError: error
      };
    }
  }

  // Default error
  return {
    type: ErrorType.UNKNOWN,
    message: error?.message || defaultMessage,
    originalError: error
  };
};

// Log error to console and potentially to an error tracking service
export const logError = (error: AppError): void => {
  // Only log to console in development
  if (import.meta.env.DEV) {
    console.error('Error:', error);
  }

  // Here you could add integration with error tracking services like Sentry
  // if (import.meta.env.PROD) {
  //   Sentry.captureException(error.originalError || error);
  // }
};

// Hook for handling errors with toast notifications
export const useErrorHandler = () => {
  const { toast } = useToast();

  const handleError = (error: any, customMessage?: string) => {
    const appError = createError(error, customMessage);
    logError(appError);

    // Show toast notification
    toast({
      title: getErrorTitle(appError.type),
      description: appError.message,
      variant: 'destructive',
      duration: 5000,
    });

    return appError;
  };

  return { handleError };
};

// Get appropriate error title based on error type
const getErrorTitle = (type: ErrorType): string => {
  switch (type) {
    case ErrorType.NETWORK:
      return 'Network Error';
    case ErrorType.AUTHENTICATION:
      return 'Authentication Error';
    case ErrorType.AUTHORIZATION:
      return 'Authorization Error';
    case ErrorType.VALIDATION:
      return 'Validation Error';
    case ErrorType.NOT_FOUND:
      return 'Not Found';
    case ErrorType.SERVER:
      return 'Server Error';
    case ErrorType.UNKNOWN:
    default:
      return 'Error';
  }
};

// Function to handle API response errors
export const handleApiResponse = async (response: Response): Promise<any> => {
  if (!response.ok) {
    let errorData;

    try {
      errorData = await response.json();
    } catch (e) {
      // If response is not JSON, use status text
      throw {
        type: ErrorType.UNKNOWN,
        message: `${response.status} ${response.statusText}`,
        statusCode: response.status
      };
    }

    // Throw standardized error
    throw {
      type: getErrorTypeFromStatus(response.status),
      message: errorData.message || `${response.status} ${response.statusText}`,
      details: errorData.details || errorData.errors,
      statusCode: response.status,
      originalError: errorData
    };
  }

  return response.json();
};

// Helper to determine error type from HTTP status
const getErrorTypeFromStatus = (status: number): ErrorType => {
  if (status === 401) return ErrorType.AUTHENTICATION;
  if (status === 403) return ErrorType.AUTHORIZATION;
  if (status === 404) return ErrorType.NOT_FOUND;
  if (status >= 400 && status < 500) return ErrorType.VALIDATION;
  if (status >= 500) return ErrorType.SERVER;
  return ErrorType.UNKNOWN;
};
