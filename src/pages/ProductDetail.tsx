import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { getProductBySlug, clearProductCache } from '../services/newApi';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogClose } from '@/components/ui/dialog';
import { ArrowLeft, ExternalLink, Loader2, ThumbsUp, ThumbsDown, X } from 'lucide-react';
import Header from '@/components/Header';
import SEOHead from '@/components/SEO/SEOHead';

const ProductDetail: React.FC = () => {
  const { slug } = useParams<{ slug: string }>();
  const navigate = useNavigate();

  const [product, setProduct] = useState<any | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showSourcesModal, setShowSourcesModal] = useState(false);

  useEffect(() => {
    if (!slug) return;

    const fetchProduct = async () => {
      setLoading(true);
      setError(null);

      try {
        // Try to fetch with force refresh to avoid stale cache
        const data = await getProductBySlug(slug, true);
        setProduct(data);
      } catch (err) {
        // If there's an error, clear the product cache to prevent future issues
        clearProductCache();
        setError(err instanceof Error ? err.message : 'Failed to fetch product details');
        console.error('Error fetching product:', err);
      } finally {
        setLoading(false);
      }
    };

    fetchProduct();
  }, [slug]);

  const getScoreColor = (score: number) => {
    if (score >= 80) return 'bg-green-100 text-green-800';
    if (score >= 70) return 'bg-green-100 text-green-800';
    if (score >= 60) return 'bg-yellow-100 text-yellow-800';
    if (score >= 50) return 'bg-yellow-100 text-yellow-800';
    if (score >= 40) return 'bg-orange-100 text-orange-800';
    return 'bg-red-100 text-red-800';
  };

  // Helper functions for source information
  const getSourceTitle = (url: string) => {
    try {
      const domain = new URL(url).hostname.replace('www.', '');
      const domainMap: { [key: string]: string } = {
        'techradar.com': 'TechRadar',
        'tomsguide.com': "Tom's Guide",
        'independent.co.uk': 'The Independent',
        'wired.com': 'WIRED',
        'theverge.com': 'The Verge',
        'engadget.com': 'Engadget',
        'cnet.com': 'CNET'
      };
      return domainMap[domain] || domain.charAt(0).toUpperCase() + domain.slice(1);
    } catch {
      return 'External Source';
    }
  };

  const getFaviconUrl = (url: string) => {
    try {
      const domain = new URL(url).hostname;
      // Use Google's favicon service for better reliability
      return `https://www.google.com/s2/favicons?domain=${domain}&sz=32`;
    } catch {
      return null;
    }
  };

  const getSourceDate = (url: string) => {
    // This would typically come from metadata, for now return a placeholder
    return new Date().toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const getSourceDescription = (url: string) => {
    try {
      const domain = new URL(url).hostname.replace('www.', '');
      const descriptions: { [key: string]: string } = {
        'techradar.com': 'Comprehensive tech reviews and analysis',
        'tomsguide.com': 'Expert product testing and recommendations',
        'independent.co.uk': 'Independent journalism and reviews',
        'wired.com': 'Technology news and in-depth analysis',
        'theverge.com': 'Technology news and product reviews',
        'engadget.com': 'Consumer technology news and reviews',
        'cnet.com': 'Technology product reviews and news'
      };
      return descriptions[domain] || 'Product information and reviews';
    } catch {
      return 'External product information';
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-background">
        <Header />
        <div className="container mx-auto px-4 py-12 flex justify-center">
          <Loader2 className="h-8 w-8 animate-spin" />
        </div>
      </div>
    );
  }

  if (error || !product) {
    return (
      <div className="min-h-screen bg-background">
        <Header />
        <div className="container mx-auto px-4 py-8">
          <Alert variant="destructive" className="mb-4">
            <AlertDescription>{error || 'Product not found'}</AlertDescription>
          </Alert>
          <Button variant="outline" onClick={() => navigate(-1)}>
            <ArrowLeft className="mr-2 h-4 w-4" /> Go Back
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-background">
      <SEOHead
        title={`${product.name} - Review & Analysis | Halocritique`}
        description={`${product.description.substring(0, 150)}... Read our detailed review and analysis of ${product.name} by ${product.brand}.`}
        keywords={[
          product.name.toLowerCase(),
          product.brand.toLowerCase(),
          product.category.toLowerCase(),
          'review',
          'analysis',
          'product review',
          'buying guide',
          ...(product.keywords || [])
        ]}
        type="product"
        image={product.image}
        url={`/products/${product.slug}`}
        brand={product.brand}
        category={product.category}
        productId={product._id}
        rating={product.score / 20} // Convert percentage to 5-star rating
        reviewCount={1}
        price={product.price > 0 ? product.price : undefined}
        availability="in stock"
        publishedTime={product.createdAt}
        modifiedTime={product.updatedAt}
        canonicalUrl={`/products/${product.slug}`}
      />
      <Header />
      <div className="container mx-auto px-4 py-8">

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 lg:gap-8 mb-8">
          <div>
            <div className="rounded-lg overflow-hidden border bg-white p-4 flex items-center justify-center h-[300px] sm:h-[400px]">
              <img
                src={product.image}
                alt={product.name}
                className="max-w-full max-h-full object-contain"
              />
            </div>
          </div>

          <div>
            <h1 className="text-xl sm:text-2xl lg:text-3xl font-bold mb-3 leading-tight">{product.name}</h1>

            <div className="flex flex-wrap items-center gap-2 mb-4">
              <Badge className="text-xs sm:text-sm">{product.category}</Badge>
              <Badge variant="outline" className="text-xs sm:text-sm">{product.brand}</Badge>
              <Badge
                className={`${getScoreColor(product.score)} text-xs sm:text-sm`}
              >
                {product.score}%
              </Badge>
            </div>

            {product.price > 0 && (
              <p className="text-xl sm:text-2xl font-bold mb-4">${product.price.toFixed(2)}</p>
            )}

            <p className="text-gray-700 mb-6 text-sm sm:text-base leading-relaxed">{product.description}</p>

            {product.affiliateLink && (
              <Button className="w-full mb-6" asChild>
                <a href={product.affiliateLink} target="_blank" rel="noopener noreferrer">
                  Check it out <ExternalLink className="ml-2 h-4 w-4" />
                </a>
              </Button>
            )}

            {/* Pros and Cons - Stacked vertically */}
            <div className="space-y-4 mb-6">
              {/* Pros Card */}
              <Card className="border-green-100 bg-green-50/30">
                <CardHeader className="p-4 pb-3">
                  <CardTitle className="text-base sm:text-lg flex items-center text-green-700">
                    <ThumbsUp className="mr-2 h-5 w-5 text-green-600" /> Pros
                  </CardTitle>
                </CardHeader>
                <CardContent className="p-4 pt-0">
                  {product.pros.length > 0 ? (
                    <ul className="list-disc pl-5 space-y-2">
                      {product.pros.map((pro: string, index: number) => (
                        <li key={index} className="text-sm sm:text-base text-gray-700 leading-relaxed">{pro}</li>
                      ))}
                    </ul>
                  ) : (
                    <p className="text-sm text-muted-foreground">No pros listed</p>
                  )}
                </CardContent>
              </Card>

              {/* Cons Card */}
              <Card className="border-red-100 bg-red-50/30">
                <CardHeader className="p-4 pb-3">
                  <CardTitle className="text-base sm:text-lg flex items-center text-red-700">
                    <ThumbsDown className="mr-2 h-5 w-5 text-red-600" /> Cons
                  </CardTitle>
                </CardHeader>
                <CardContent className="p-4 pt-0">
                  {product.cons.length > 0 ? (
                    <ul className="list-disc pl-5 space-y-2">
                      {product.cons.map((con: string, index: number) => (
                        <li key={index} className="text-sm sm:text-base text-gray-700 leading-relaxed">{con}</li>
                      ))}
                    </ul>
                  ) : (
                    <p className="text-sm text-muted-foreground">No cons listed</p>
                  )}
                </CardContent>
              </Card>
            </div>
          </div>
        </div>

        {/* Score Section with Integrated Sources */}
        <div className="mt-8">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <div className={`w-3 h-3 rounded-full ${getScoreColor(product.score).includes('green') ? 'bg-green-500' :
                  getScoreColor(product.score).includes('yellow') ? 'bg-yellow-500' : 'bg-red-500'}`}></div>
                Score
              </CardTitle>
              <CardDescription>
                Overall product rating based on our analysis
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="flex items-center justify-between">
                {/* Score Display */}
                <div>
                  <div className={`inline-block px-4 py-2 rounded-full text-lg font-bold ${getScoreColor(product.score)}`}>
                    {product.score}%
                  </div>
                  <p className="mt-3 text-sm text-muted-foreground">
                    {product.score >= 80 ? 'Excellent' :
                      product.score >= 70 ? 'Very Good' :
                        product.score >= 60 ? 'Good' :
                          product.score >= 50 ? 'Average' :
                            product.score >= 40 ? 'Below Average' : 'Poor'}
                  </p>
                </div>

                {/* Sources Display */}
                {product.sources && product.sources.length > 0 && (
                  <div className="flex items-center gap-3">
                    {/* Source Favicons */}
                    <div className="flex items-center gap-1">
                      {product.sources.slice(0, 3).map((source: string, index: number) => {
                        const faviconUrl = getFaviconUrl(source);
                        return (
                          <div key={index} className="w-6 h-6 rounded-full overflow-hidden bg-white border border-gray-200 flex items-center justify-center">
                            {source.startsWith('http') && faviconUrl ? (
                              <img
                                src={faviconUrl}
                                alt={getSourceTitle(source)}
                                className="w-4 h-4 object-contain"
                                onError={(e) => {
                                  // Fallback to external link icon
                                  const target = e.target as HTMLImageElement;
                                  target.style.display = 'none';
                                  const parent = target.parentElement;
                                  if (parent) {
                                    parent.innerHTML = '<svg class="h-3 w-3 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"></path></svg>';
                                  }
                                }}
                              />
                            ) : (
                              <ExternalLink className="h-3 w-3 text-gray-600" />
                            )}
                          </div>
                        );
                      })}
                      {product.sources.length > 3 && (
                        <div className="w-6 h-6 rounded-full bg-gray-100 border border-gray-200 flex items-center justify-center">
                          <span className="text-gray-600 text-xs font-medium">+{product.sources.length - 3}</span>
                        </div>
                      )}
                    </div>

                    {/* Sources Button */}
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setShowSourcesModal(true)}
                      className="flex items-center gap-2 text-sm"
                    >
                      <span>Sources</span>
                      <ExternalLink className="h-3 w-3" />
                    </Button>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Sources Modal */}
        <Dialog open={showSourcesModal} onOpenChange={setShowSourcesModal}>
          <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
            <DialogHeader>
              <DialogTitle className="flex items-center gap-2">
                <div className="flex -space-x-1">
                  <div className="w-3 h-3 rounded-full bg-blue-500"></div>
                  <div className="w-3 h-3 rounded-full bg-green-500"></div>
                  <div className="w-3 h-3 rounded-full bg-purple-500"></div>
                </div>
                Sources
              </DialogTitle>
            </DialogHeader>

            <div className="mt-4">
              <h3 className="text-lg font-semibold mb-4">Citations</h3>
              {product?.sources && product.sources.length > 0 ? (
                <div className="space-y-4">
                  {product.sources.map((source: string, index: number) => (
                    <div key={index} className="border-l-4 border-blue-500 pl-4 py-2">
                      <div className="flex items-start gap-3">
                        <div className="flex-shrink-0 mt-1">
                          {source.startsWith('http') ? (
                            <div className="w-6 h-6 rounded-full overflow-hidden bg-white border border-gray-200 flex items-center justify-center">
                              <img
                                src={getFaviconUrl(source)}
                                alt={getSourceTitle(source)}
                                className="w-4 h-4 object-contain"
                                onError={(e) => {
                                  const target = e.target as HTMLImageElement;
                                  target.style.display = 'none';
                                  const parent = target.parentElement;
                                  if (parent) {
                                    parent.innerHTML = '<svg class="h-3 w-3 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"></path></svg>';
                                  }
                                }}
                              />
                            </div>
                          ) : (
                            <div className="w-6 h-6 rounded-full bg-gray-100 border border-gray-200 flex items-center justify-center">
                              <div className="w-2 h-2 bg-gray-600 rounded-full"></div>
                            </div>
                          )}
                        </div>
                        <div className="flex-1 min-w-0">
                          {source.startsWith('http') ? (
                            <div>
                              <div className="font-medium text-sm text-gray-900 mb-1">
                                {getSourceTitle(source)}
                              </div>
                              <a
                                href={source}
                                target="_blank"
                                rel="noopener noreferrer"
                                className="text-blue-600 hover:text-blue-800 text-sm break-all"
                              >
                                {source}
                              </a>
                              <p className="text-xs text-gray-500 mt-1">
                                {getSourceDate(source)} — {getSourceDescription(source)}
                              </p>
                            </div>
                          ) : (
                            <div>
                              <div className="font-medium text-sm text-gray-900 mb-1">
                                {source}
                              </div>
                              <p className="text-xs text-gray-500">
                                Editorial source
                              </p>
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <p className="text-sm text-muted-foreground">No sources available</p>
              )}
            </div>
          </DialogContent>
        </Dialog>
      </div>
    </div>
  );
};

export default ProductDetail;
