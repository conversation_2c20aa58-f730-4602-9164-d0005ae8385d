import React from 'react';
import { useNavigate } from 'react-router-dom';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import Header from '../components/Header';
import SEOHead from '../components/SEO/SEOHead';
import {
  Star,
  Users,
  Search,
  Shield,
  Heart,
  Target,
  Globe,
  TrendingUp,
  CheckCircle,
  ArrowRight,
  Quote
} from 'lucide-react';

const AboutPage: React.FC = () => {
  const navigate = useNavigate();

  const stats = [
    { number: '25,000+', label: 'Product Opinions', icon: <Star className="h-6 w-6 text-yellow-500" /> },
    { number: '1,200+', label: 'Brands Covered', icon: <Globe className="h-6 w-6 text-blue-500" /> },
    { number: '50+', label: 'Categories', icon: <TrendingUp className="h-6 w-6 text-green-500" /> },
    { number: '100%', label: 'Unbiased Reviews', icon: <Shield className="h-6 w-6 text-teal-500" /> }
  ];

  const values = [
    {
      icon: <Shield className="h-10 w-10 text-teal-600" />,
      title: 'Radical Transparency',
      description: 'Every opinion on Halocritique is authentic and unfiltered. We never accept payment for positive reviews or hide negative feedback.',
      highlight: 'Zero bias guarantee'
    },
    {
      icon: <Heart className="h-10 w-10 text-rose-500" />,
      title: 'Consumer Empowerment',
      description: 'We put the power back in your hands with detailed insights, pros & cons, and honest scoring to help you choose wisely.',
      highlight: 'Your success is our mission'
    },
    {
      icon: <Target className="h-10 w-10 text-blue-600" />,
      title: 'Quality Excellence',
      description: 'Our expert team curates every product opinion, ensuring you get comprehensive, reliable information every time.',
      highlight: 'Expert-verified content'
    }
  ];



  return (
    <div className="min-h-screen bg-background">
      <SEOHead
        title="About Halocritique - Honest Product Reviews & Comparisons"
        description="Learn about Halocritique's mission to provide honest, unbiased product reviews and comparisons. Discover how we help consumers make informed purchasing decisions with expert-curated opinions."
        keywords={[
          'about halocritique',
          'honest product reviews',
          'unbiased reviews',
          'product comparison platform',
          'consumer reviews',
          'product analysis',
          'buying guide platform',
          'review aggregator'
        ]}
        type="article"
        url="/about"
        author="Halocritique Team"
        category="About"
        canonicalUrl="/about"
      />
      <Header />

      {/* Breadcrumb */}
      <div className="bg-gray-50 border-b">
        <div className="container mx-auto px-4 py-2">
          <div className="flex items-center text-sm text-gray-600">
            <Button variant="link" className="p-0 h-auto" onClick={() => navigate('/')}>
              Home
            </Button>
            <span className="mx-2">/</span>
            <span className="text-gray-900 font-medium">About</span>
          </div>
        </div>
      </div>

      {/* Hero Section */}
      <section className="relative py-20 bg-gradient-to-br from-slate-50 via-teal-50 to-blue-50 overflow-hidden">
        {/* Background Pattern */}
        <div className="absolute inset-0 opacity-5">
          <div className="absolute top-10 left-10 w-20 h-20 border-2 border-teal-600 rounded-full"></div>
          <div className="absolute top-32 right-20 w-16 h-16 border-2 border-blue-600 rounded-full"></div>
          <div className="absolute bottom-20 left-1/4 w-12 h-12 border-2 border-rose-600 rounded-full"></div>
          <div className="absolute bottom-32 right-1/3 w-24 h-24 border-2 border-teal-600 rounded-full"></div>
        </div>

        <div className="container mx-auto px-4 text-center relative z-10">
          <div className="max-w-5xl mx-auto">
            {/* Badge */}
            <div className="inline-flex items-center px-4 py-2 bg-teal-100 text-teal-800 rounded-full text-sm font-medium mb-8">
              <Star className="h-4 w-4 mr-2" />
              Trusted by thousands of smart shoppers
            </div>

            <h1 className="text-5xl md:text-7xl font-bold text-gray-900 mb-8 leading-tight">
              We're <span className="halocritique-logo text-transparent bg-gradient-to-r from-teal-600 to-blue-600 bg-clip-text">Halocritique</span>.
            </h1>

            <p className="text-2xl md:text-3xl text-gray-700 mb-8 leading-relaxed font-light">
              The world's most trusted platform for
              <span className="text-teal-600 font-semibold"> honest product opinions</span>
            </p>

            <p className="text-xl text-gray-600 mb-12 max-w-3xl mx-auto leading-relaxed">
              Born from frustration with biased reviews and fake ratings, Halocritique delivers authentic,
              expert-curated product insights that help you make confident purchasing decisions every time.
            </p>

            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button
                size="lg"
                onClick={() => navigate('/')}
                className="bg-gradient-to-r from-teal-600 to-blue-600 hover:from-teal-700 hover:to-blue-700 text-white px-8 py-4 text-lg"
              >
                Explore Products <ArrowRight className="ml-2 h-5 w-5" />
              </Button>
              <Button
                size="lg"
                variant="outline"
                onClick={() => navigate('/brands')}
                className="border-2 border-teal-600 text-teal-600 hover:bg-teal-50 px-8 py-4 text-lg"
              >
                View Brands
              </Button>
            </div>
          </div>
        </div>
      </section>

      {/* What We Do Section */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              What We Do
            </h2>
            <p className="text-lg text-gray-600 max-w-3xl mx-auto">
              We bring consumers and products together to create better shopping experiences for everyone.
            </p>
          </div>

          <div className="grid md:grid-cols-2 gap-8 max-w-4xl mx-auto">
            {/* Consumers */}
            <Card className="p-8 text-center hover:shadow-lg transition-shadow">
              <CardContent className="p-0">
                <div className="w-16 h-16 bg-teal-100 rounded-full flex items-center justify-center mx-auto mb-6">
                  <Users className="h-8 w-8 text-teal-600" />
                </div>
                <h3 className="text-xl font-semibold text-gray-900 mb-2">Consumers:</h3>
                <p className="text-teal-600 font-medium mb-4">Discover honest opinions</p>
                <p className="text-gray-600">
                  Find detailed, unbiased opinions about products from trusted sources to make confident purchasing decisions.
                </p>
              </CardContent>
            </Card>

            {/* Brands */}
            <Card className="p-8 text-center hover:shadow-lg transition-shadow">
              <CardContent className="p-0">
                <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-6">
                  <Search className="h-8 w-8 text-blue-600" />
                </div>
                <h3 className="text-xl font-semibold text-gray-900 mb-2">Brands:</h3>
                <p className="text-blue-600 font-medium mb-4">Gain visibility</p>
                <p className="text-gray-600">
                  Showcase your products to consumers actively searching for honest opinions and quality recommendations.
                </p>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* Stats Section */}
      <section className="py-20 bg-white">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Trusted by Thousands, Powered by Data
            </h2>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              Our commitment to authentic product opinions has built a community of informed consumers
            </p>
          </div>

          <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
            {stats.map((stat, index) => (
              <Card key={index} className="p-8 text-center hover:shadow-xl transition-all duration-300 border-0 bg-gradient-to-br from-white to-gray-50">
                <CardContent className="p-0">
                  <div className="flex justify-center mb-4">
                    <div className="p-3 bg-white rounded-full shadow-lg">
                      {stat.icon}
                    </div>
                  </div>
                  <div className="text-4xl md:text-5xl font-bold text-gray-900 mb-3">
                    {stat.number}
                  </div>
                  <div className="text-gray-600 font-medium">
                    {stat.label}
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Mission & Vision */}
      <section className="py-20 bg-gradient-to-br from-slate-900 via-gray-900 to-teal-900 text-white relative overflow-hidden">
        {/* Background Elements */}
        <div className="absolute inset-0 opacity-10">
          <div className="absolute top-20 left-10 w-32 h-32 border border-teal-400 rounded-full"></div>
          <div className="absolute bottom-20 right-10 w-24 h-24 border border-blue-400 rounded-full"></div>
          <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 border border-teal-400 rounded-full"></div>
        </div>

        <div className="container mx-auto px-4 relative z-10">
          <div className="max-w-5xl mx-auto text-center">
            <div className="mb-8">
              <Badge className="bg-teal-600 text-white px-4 py-2 text-sm font-medium">
                Our Mission
              </Badge>
            </div>

            <h2 className="text-4xl md:text-5xl font-bold mb-8 leading-tight">
              Revolutionizing How You
              <span className="text-transparent bg-gradient-to-r from-teal-400 to-blue-400 bg-clip-text"> Discover Products</span>
            </h2>

            <div className="grid md:grid-cols-2 gap-12 items-center">
              <div className="text-left">
                <Quote className="h-16 w-16 text-teal-400 mb-6" />
                <blockquote className="text-xl md:text-2xl text-gray-200 mb-8 leading-relaxed">
                  "We're not just another review site. We're building the future of consumer decision-making—where
                  every opinion is authentic, every insight is valuable, and every purchase is confident."
                </blockquote>

                <div className="flex items-center">
                  {/* <div className="w-16 h-16 bg-gradient-to-br from-teal-500 to-blue-500 rounded-full flex items-center justify-center mr-4">
                    <span className="text-white font-bold text-lg">SJ</span>
                  </div> */}
                  {/* <div>
                    <div className="font-semibold text-white text-lg">Sarah Johnson</div>
                    <div className="text-teal-300">Founder & CEO, Halo</div>
                    <div className="text-gray-400 text-sm">Former VP of Product, Amazon</div>
                  </div> */}
                </div>
              </div>

              <div className="space-y-6">
                <div className="bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20">
                  <h3 className="text-xl font-semibold text-teal-300 mb-3">Our Promise</h3>
                  <p className="text-gray-200">
                    Every product opinion on Halocritique is carefully curated, fact-checked, and presented without bias.
                    We never accept payment for positive reviews.
                  </p>
                </div>

                <div className="bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20">
                  <h3 className="text-xl font-semibold text-blue-300 mb-3">Our Impact</h3>
                  <p className="text-gray-200">
                    Helping consumers save time, money, and frustration by providing the honest insights
                    they need to make perfect purchasing decisions.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Our Values */}
      <section className="py-20 bg-gradient-to-br from-gray-50 to-white">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <Badge className="bg-teal-100 text-teal-800 px-4 py-2 text-sm font-medium mb-6">
              What Drives Us
            </Badge>
            <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
              Our Core Values
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              These principles aren't just words on a wall—they're the foundation of every decision we make
            </p>
          </div>

          <div className="grid md:grid-cols-3 gap-8 max-w-7xl mx-auto">
            {values.map((value, index) => (
              <Card key={index} className="group p-8 text-center hover:shadow-2xl transition-all duration-500 border-0 bg-white hover:-translate-y-2">
                <CardContent className="p-0">
                  <div className="mb-6 relative">
                    <div className="w-20 h-20 mx-auto bg-gradient-to-br from-gray-100 to-gray-200 rounded-2xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                      {value.icon}
                    </div>
                  </div>

                  <Badge className="bg-teal-50 text-teal-700 px-3 py-1 text-xs font-medium mb-4">
                    {value.highlight}
                  </Badge>

                  <h3 className="text-2xl font-bold text-gray-900 mb-4">
                    {value.title}
                  </h3>

                  <p className="text-gray-600 leading-relaxed text-lg">
                    {value.description}
                  </p>

                  <div className="mt-6 pt-6 border-t border-gray-100">
                    <div className="flex items-center justify-center text-teal-600 font-medium text-sm">
                      <CheckCircle className="h-4 w-4 mr-2" />
                      Verified by our community
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>



      {/* How It Works */}
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              How Halocritique Works
            </h2>
            <p className="text-lg text-gray-600 max-w-3xl mx-auto">
              Our simple process helps you find the best products with confidence.
            </p>
          </div>

          <div className="grid md:grid-cols-3 gap-8 max-w-6xl mx-auto">
            <div className="text-center">
              <div className="w-16 h-16 bg-teal-600 rounded-full flex items-center justify-center mx-auto mb-6">
                <span className="text-white text-2xl font-bold">1</span>
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-3">
                Search Products
              </h3>
              <p className="text-gray-600">
                Use our powerful search to find products across categories and brands.
                Our smart search understands what you're looking for.
              </p>
            </div>

            <div className="text-center">
              <div className="w-16 h-16 bg-teal-600 rounded-full flex items-center justify-center mx-auto mb-6">
                <span className="text-white text-2xl font-bold">2</span>
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-3">
                Read Honest Opinions
              </h3>
              <p className="text-gray-600">
                Get detailed insights including pros, cons, and expert scores.
                All opinions are curated for quality and relevance.
              </p>
            </div>

            <div className="text-center">
              <div className="w-16 h-16 bg-teal-600 rounded-full flex items-center justify-center mx-auto mb-6">
                <span className="text-white text-2xl font-bold">3</span>
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-3">
                Make Confident Decisions
              </h3>
              <p className="text-gray-600">
                Use our insights to choose the right product for your needs.
                Check it out through our trusted retail partners.
              </p>
            </div>
          </div>
        </div>
      </section>



      {/* Footer */}
      <footer className="border-t py-8">
        <div className="container mx-auto px-4">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <div className="flex items-center space-x-2 mb-4 md:mb-0">
              <Star className="h-5 w-5 text-primary" />
              <span className="text-2xl halocritique-logo">Halocritique</span>
            </div>

            <div className="text-sm text-muted-foreground">
              © {new Date().getFullYear()} Halocritique. All rights
              reserved.
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
};

export default AboutPage;
