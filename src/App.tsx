import { Suspense, lazy, useEffect, useState } from "react";
import { Routes, Route } from "react-router-dom";
import { HelmetProvider } from "react-helmet-async";
import { Toaster } from "@/components/ui/toaster";
import ErrorBoundary from "./components/ErrorBoundary";
import { ConnectionStatus, PageLoading } from "./components/ui/loading-states";
import { networkMonitor } from "./utils/networkUtils";
import { config } from "./config/environment";

// Lazy-loaded components for code splitting
const Home = lazy(() => import("./components/home"));
const NewSearchResults = lazy(() => import("./components/NewSearchResults"));
const AdminPage = lazy(() => import("./pages/AdminPage"));
const ProductDetail = lazy(() => import("./pages/ProductDetail"));
const CategoriesPage = lazy(() => import("./pages/CategoriesPage"));
const BrandsPage = lazy(() => import("./pages/BrandsPage"));
const AboutPage = lazy(() => import("./pages/AboutPage"));

// Loading fallback component
const LoadingFallback = () => <PageLoading message="Loading application..." />;

function App() {
  const [isOnline, setIsOnline] = useState(navigator.onLine);

  // Monitor network status
  useEffect(() => {
    const handleNetworkChange = (online: boolean) => {
      setIsOnline(online);
    };

    networkMonitor.addListener(handleNetworkChange);

    return () => {
      networkMonitor.removeListener(handleNetworkChange);
    };
  }, []);

  return (
    <HelmetProvider>
      <ErrorBoundary>
        <div className="min-h-screen bg-background overflow-x-hidden">
          <ConnectionStatus isOnline={isOnline} />
          <Suspense fallback={<LoadingFallback />}>
            {/* Main application routes */}
            <Routes>
              <Route path="/" element={<Home />} />
              <Route path="/search" element={<NewSearchResults />} />
              <Route path="/categories" element={<CategoriesPage />} />
              <Route path="/brands" element={<BrandsPage />} />
              <Route path="/about" element={<AboutPage />} />
              <Route path="/products/:slug" element={<ProductDetail />} />
              <Route path="/admin" element={<AdminPage />} />
            </Routes>
          </Suspense>

          {/* Toast notifications */}
          <Toaster />
        </div>
      </ErrorBoundary>
    </HelmetProvider>
  );
}

export default App;
