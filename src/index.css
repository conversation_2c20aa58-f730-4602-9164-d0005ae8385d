@import url('./fonts.css');
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;

    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    /* Trustpilot-inspired teal color */
    --primary: 166 71% 35%;
    --primary-foreground: 210 40% 98%;

    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;

    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 166 71% 35%;

    /* Rating stars color */
    --rating: 45 100% 50%;

    /* Score colors */
    --score-excellent: 142 76% 36%;
    --score-good: 166 71% 35%;
    --score-average: 48 96% 53%;
    --score-poor: 0 84% 60%;

    --radius: 0.5rem;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  html,
  body {
    @apply bg-background text-foreground;
    overflow-x: hidden;
    max-width: 100vw;
  }

  body {
    position: relative;
  }

  /* Prevent horizontal scrolling on all elements */
  * {
    max-width: 100%;
    box-sizing: border-box;
  }
}

/* Blob animations for hero section - minimal movement */
@keyframes blob {
  0% {
    transform: translate(0px, 0px) scale(1);
  }

  33% {
    transform: translate(8px, -12px) scale(1.02);
  }

  66% {
    transform: translate(-5px, 5px) scale(0.98);
  }

  100% {
    transform: translate(0px, 0px) scale(1);
  }
}

.animate-blob {
  animation: blob 7s infinite;
}

.animation-delay-2000 {
  animation-delay: 2s;
}

.animation-delay-4000 {
  animation-delay: 4s;
}

/* Hide scrollbar for categories grid */
.scrollbar-hide {
  -ms-overflow-style: none;
  scrollbar-width: none;
}

.scrollbar-hide::-webkit-scrollbar {
  display: none;
}

/* Mobile-first responsive utilities */
@layer utilities {

  /* Touch-friendly interactions */
  .touch-manipulation {
    touch-action: manipulation;
  }

  /* Line clamping for text truncation */
  .line-clamp-1 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 1;
  }

  .line-clamp-2 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
  }

  .line-clamp-3 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 3;
  }

  /* Safe area insets for mobile devices */
  .safe-top {
    padding-top: env(safe-area-inset-top);
  }

  .safe-bottom {
    padding-bottom: env(safe-area-inset-bottom);
  }

  .safe-left {
    padding-left: env(safe-area-inset-left);
  }

  .safe-right {
    padding-right: env(safe-area-inset-right);
  }

  /* Smooth scrolling for better UX */
  .smooth-scroll {
    scroll-behavior: smooth;
    -webkit-overflow-scrolling: touch;
  }

  /* Prevent text selection on interactive elements */
  .no-select {
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
  }

  /* Focus visible for better accessibility */
  .focus-visible-ring {
    @apply focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-teal-500 focus-visible:ring-offset-2;
  }

  /* Prevent horizontal overflow on mobile */
  .mobile-container {
    width: 100%;
    max-width: 100vw;
    overflow-x: hidden;
  }

  /* Ensure all containers respect viewport width */
  .container {
    max-width: 100vw;
    overflow-x: hidden;
  }

  /* Utility to prevent horizontal overflow */
  .no-horizontal-overflow {
    overflow-x: hidden;
    max-width: 100vw;
    width: 100%;
  }

  /* Debug utility - uncomment to find overflow elements */
  /*
  * {
    outline: 1px solid red !important;
  }
  */
}