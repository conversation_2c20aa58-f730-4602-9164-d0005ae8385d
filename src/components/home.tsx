import { Card, CardContent } from "./ui/card";
import { Star } from "lucide-react";
import { useNavigate } from "react-router-dom";
import Header from "./Header";
import SimpleSearchAutocomplete from "./SimpleSearchAutocomplete";
import ProductBrandsGrid from "./ProductCategoriesGrid";
import CommunityCallToAction from "./CommunityCallToAction";
import SEOHead from "./SEO/SEOHead";

const Home = () => {
  const navigate = useNavigate();

  return (
    <div className="min-h-screen bg-background overflow-x-hidden">
      <SEOHead
        title="Halocritique - Honest Product Reviews & Comparisons"
        description="Find honest, unbiased product reviews and comparisons. Make informed purchasing decisions with expert-curated opinions and detailed analysis from trusted sources."
        keywords={[
          'product reviews',
          'product comparisons',
          'honest reviews',
          'buying guide',
          'product opinions',
          'consumer reviews',
          'product analysis',
          'unbiased reviews',
          'shopping guide',
          'product research'
        ]}
        type="website"
        url="/"
      />

      {/* Header */}
      <Header />

      {/* Hero Section */}
      <section className="py-12 sm:py-16 lg:py-20 bg-gradient-to-br from-gray-50 to-gray-100 relative overflow-hidden">
        {/* Background decorative elements - safely contained */}
        <div className="absolute top-8 left-8 w-32 h-32 sm:w-40 sm:h-40 lg:w-48 lg:h-48 bg-teal-200 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-blob"></div>
        <div className="absolute top-8 right-8 w-32 h-32 sm:w-40 sm:h-40 lg:w-48 lg:h-48 bg-yellow-200 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-blob animation-delay-2000"></div>
        <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 w-32 h-32 sm:w-40 sm:h-40 lg:w-48 lg:h-48 bg-pink-200 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-blob animation-delay-4000"></div>

        <div className="container mx-auto px-4 sm:px-6 lg:px-8 text-center relative z-10">
          <h1 className="text-2xl xs:text-3xl sm:text-4xl md:text-5xl lg:text-6xl font-bold mb-4 sm:mb-6 text-gray-900 leading-tight">
            We’ve done the research so you don’t have to
          </h1>
          <p className="text-base sm:text-lg md:text-xl lg:text-2xl text-gray-600 mb-8 sm:mb-12 max-w-3xl mx-auto leading-relaxed px-4 sm:px-0">
            Find trusted affordable products, backed by real data not hype.
          </p>

          {/* Search Form */}
          <div className="max-w-2xl mx-auto px-4 sm:px-0">
            <SimpleSearchAutocomplete
              placeholder="Search for a product..."
              className="w-full"
              onSearch={(query) => {
                if (query.trim()) {
                  navigate(`/search?q=${encodeURIComponent(query)}`);
                }
              }}
            />

            {/* Popular searches */}
            <div className="mt-4 sm:mt-6 flex flex-wrap justify-center gap-2 px-2 sm:px-0">
              <span className="text-xs sm:text-sm text-gray-500 mb-2 sm:mb-0">Popular:</span>
              {['iPhone 15', 'MacBook Pro', 'AirPods', 'Samsung TV'].map((term) => (
                <button
                  key={term}
                  onClick={() => navigate(`/search?q=${encodeURIComponent(term)}`)}
                  className="text-xs sm:text-sm bg-white hover:bg-gray-50 active:bg-gray-100 text-gray-700 px-2 sm:px-3 py-1 rounded-full border border-gray-200 transition-colors touch-manipulation"
                >
                  {term}
                </button>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Product Brands Grid */}
      <ProductBrandsGrid />

      {/* Community Call to Action */}
      <CommunityCallToAction />

      {/* Features Section */}
      <section className="py-12 sm:py-16 bg-muted">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <h2 className="text-xl sm:text-2xl lg:text-3xl font-bold mb-6 sm:mb-8 text-center">
            Why Use <span className="halocritique-logo">Halocritique</span>?
          </h2>

          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6 lg:gap-8">
            <Card className="h-full">
              <CardContent className="pt-4 sm:pt-6 p-4 sm:p-6">
                <h3 className="text-lg sm:text-xl font-bold mb-2 sm:mb-3">
                  Real Opinions, Not Just Reviews
                </h3>
                <p className="text-muted-foreground text-sm sm:text-base leading-relaxed">
                  We analyze reviews to give you the honest truth, not just a star rating. Know what truly matters.
                </p>
              </CardContent>
            </Card>

            <Card className="h-full">
              <CardContent className="pt-4 sm:pt-6 p-4 sm:p-6">
                <h3 className="text-lg sm:text-xl font-bold mb-2 sm:mb-3">No BS. Ever</h3>
                <p className="text-muted-foreground text-sm sm:text-base leading-relaxed">
                  Every product listed is legitimate and meets our quality standard. Say goodbye to scams, dropshipped and low-quality Bs.
                </p>
              </CardContent>
            </Card>

            <Card className="h-full sm:col-span-2 lg:col-span-1">
              <CardContent className="pt-4 sm:pt-6 p-4 sm:p-6">
                <h3 className="text-lg sm:text-xl font-bold mb-2 sm:mb-3">Shop Smart, Buy Confidently</h3>
                <p className="text-muted-foreground text-sm sm:text-base leading-relaxed">
                  Quick, clear insights help you choose better, faster. Your time is valuable, your purchases are safe.
                </p>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="border-t py-8">
        <div className="container mx-auto px-4">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <div className="flex items-center space-x-2 mb-4 md:mb-0">
              <Star className="h-5 w-5 text-primary" />
              <span className="text-2xl halocritique-logo">Halocritique</span>
            </div>

            <div className="text-sm text-muted-foreground">
              © {new Date().getFullYear()} Halocritique. All rights
              reserved.
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
};

export default Home;
