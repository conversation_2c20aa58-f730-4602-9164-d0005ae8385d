import React, { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import {
  Smartphone,
  Laptop,
  Headphones,
  Watch,
  Camera,
  Gamepad2,
  Home,
  Car,
  Shirt,
  Dumbbell,
  Baby,
  Book,
  ChevronLeft,
  ChevronRight,
  Monitor,
  Sofa,
  Utensils,
  Bed,
  Palette,
  Sparkles,
  Heart,
  Music,
  Briefcase,
  PawPrint,
  ShoppingBag,
  Hammer,
  TreePine,
  Plane,
  Wrench,
  Package,
  Building2
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { getBrands } from "../services/newApi";

interface Brand {
  id: string;
  name: string;
  logo?: string | null;
  category?: string;
  icon: React.ReactNode;
}

// Function to get appropriate icon for a category
export const getCategoryIcon = (categoryName: string): React.ReactNode => {
  const name = categoryName.toLowerCase();

  // Category-specific icon mappings - responsive sizing handled by parent container
  if (name.includes('electronics') || name.includes('tech')) return <Smartphone />;
  if (name.includes('beauty') || name.includes('skincare')) return <Heart />;
  if (name.includes('home') || name.includes('kitchen')) return <Home />;
  if (name.includes('fashion') || name.includes('apparel')) return <Shirt />;
  if (name.includes('watch') || name.includes('jewelry') || name.includes('footwear')) return <Watch />;
  if (name.includes('sports') || name.includes('fitness')) return <Dumbbell />;
  if (name.includes('automotive') || name.includes('car')) return <Car />;
  if (name.includes('book') || name.includes('media')) return <Book />;
  if (name.includes('toy') || name.includes('game')) return <Gamepad2 />;
  if (name.includes('health') || name.includes('medical')) return <Heart />;

  // Default icon
  return <Package />;
};

// Function to get appropriate icon for a brand
export const getBrandIcon = (brandName: string): React.ReactNode => {
  const name = brandName.toLowerCase();

  // Brand-specific icon mappings - responsive sizing handled by parent container
  // Tech Giants
  if (name.includes('apple')) return <Smartphone />;
  if (name.includes('samsung')) return <Smartphone />;
  if (name.includes('google')) return <Smartphone />;
  if (name.includes('microsoft')) return <Laptop />;
  if (name.includes('sony')) return <Camera />;

  // Computing
  if (name.includes('dell')) return <Laptop />;
  if (name.includes('hp')) return <Laptop />;
  if (name.includes('lenovo')) return <Laptop />;
  if (name.includes('asus')) return <Laptop />;
  if (name.includes('acer')) return <Laptop />;

  // Automotive
  if (name.includes('tesla')) return <Car />;
  if (name.includes('bmw')) return <Car />;
  if (name.includes('mercedes')) return <Car />;
  if (name.includes('toyota')) return <Car />;
  if (name.includes('ford')) return <Car />;

  // Gaming
  if (name.includes('nintendo')) return <Gamepad2 />;
  if (name.includes('playstation')) return <Gamepad2 />;
  if (name.includes('xbox')) return <Gamepad2 />;
  if (name.includes('razer')) return <Gamepad2 />;
  if (name.includes('logitech')) return <Gamepad2 />;

  // Fashion
  if (name.includes('nike')) return <Shirt />;
  if (name.includes('adidas')) return <Shirt />;
  if (name.includes('zara')) return <Shirt />;
  if (name.includes('h&m')) return <Shirt />;
  if (name.includes('uniqlo')) return <Shirt />;

  // Audio
  if (name.includes('bose')) return <Headphones />;
  if (name.includes('beats')) return <Headphones />;
  if (name.includes('sennheiser')) return <Headphones />;

  // Watches
  if (name.includes('rolex')) return <Watch />;
  if (name.includes('omega')) return <Watch />;
  if (name.includes('casio')) return <Watch />;

  // Default icon for unknown brands
  return <Building2 />;
};

const ProductBrandsGrid: React.FC = () => {
  const navigate = useNavigate();
  const [brands, setBrands] = useState<Brand[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Fetch brands from backend on component mount
  useEffect(() => {
    const fetchBrands = async () => {
      try {
        setLoading(true);
        setError(null);

        const brandData = await getBrands();

        // Transform backend data to component format
        const transformedBrands: Brand[] = brandData.map((brand: any) => {
          // Handle both old format (string) and new format (object)
          let brandName: string;
          let brandLogo: string | null = null;
          let brandCategory: string = 'Other';

          if (typeof brand === 'string') {
            brandName = brand;
          } else if (brand && typeof brand === 'object' && brand.name) {
            brandName = brand.name || 'Unknown';
            brandLogo = brand.logo || null;
            brandCategory = brand.category || 'Other';
          } else {
            // Fallback for unexpected data structure
            brandName = String(brand) || 'Unknown';
          }

          return {
            id: brandName,
            name: brandName,
            logo: brandLogo,
            category: brandCategory,
            icon: getBrandIcon(brandName)
          };
        });

        setBrands(transformedBrands);
      } catch (err) {
        console.error('Error fetching brands:', err);
        setError('Failed to load brands');
      } finally {
        setLoading(false);
      }
    };

    fetchBrands();
  }, []);

  const handleBrandClick = (brandName: string) => {
    navigate(`/search?brand=${encodeURIComponent(brandName)}`);
  };

  const handleSeeMore = () => {
    navigate('/brands');
  };

  // Show loading state
  if (loading) {
    return (
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <div className="flex items-center justify-center">
            <div className="text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
              <p className="text-gray-500">Loading brands...</p>
            </div>
          </div>
        </div>
      </section>
    );
  }

  // Show error state
  if (error) {
    return (
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <div className="text-center">
            <p className="text-red-500 mb-4">{error}</p>
            <Button
              onClick={() => window.location.reload()}
              variant="outline"
            >
              Try Again
            </Button>
          </div>
        </div>
      </section>
    );
  }

  // Don't render if no brands
  if (brands.length === 0) {
    return null;
  }

  return (
    <section className="py-8 sm:py-12 lg:py-16 bg-white">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="mb-6 sm:mb-8">
          {/* Mobile Layout - Same Line */}
          <div className="flex items-center justify-between sm:hidden">
            <h2 className="text-xl font-bold text-gray-900 text-justify">
              Check Brands
            </h2>
            <Button
              variant="outline"
              className="text-teal-600 border-teal-600 hover:bg-teal-50 active:bg-teal-100 touch-manipulation px-3 py-2 text-sm font-medium"
              onClick={handleSeeMore}
            >
              See more
            </Button>
          </div>

          {/* Desktop Layout - Single Row */}
          <div className="hidden sm:flex sm:items-center sm:justify-between">
            <h2 className="text-2xl lg:text-3xl font-bold text-gray-900">
              Check Brands
            </h2>

            {/* Desktop controls */}
            <div className="flex items-center gap-2">
              {/* Navigation arrows */}
              <Button
                variant="outline"
                size="icon"
                className="h-9 w-9 lg:h-10 lg:w-10 rounded-full border-gray-300 hover:bg-gray-50 touch-manipulation"
                onClick={() => {
                  const container = document.getElementById('brands-grid');
                  if (container) {
                    const scrollAmount = window.innerWidth < 1024 ? 200 : 300;
                    container.scrollBy({ left: -scrollAmount, behavior: 'smooth' });
                  }
                }}
                aria-label="Scroll left"
              >
                <ChevronLeft className="h-4 w-4" />
              </Button>

              <Button
                variant="outline"
                size="icon"
                className="h-9 w-9 lg:h-10 lg:w-10 rounded-full border-gray-300 hover:bg-gray-50 touch-manipulation"
                onClick={() => {
                  const container = document.getElementById('brands-grid');
                  if (container) {
                    const scrollAmount = window.innerWidth < 1024 ? 200 : 300;
                    container.scrollBy({ left: scrollAmount, behavior: 'smooth' });
                  }
                }}
                aria-label="Scroll right"
              >
                <ChevronRight className="h-4 w-4" />
              </Button>

              {/* See more button */}
              <Button
                variant="outline"
                className="ml-2 text-teal-600 border-teal-600 hover:bg-teal-50 touch-manipulation px-6 py-3 text-base font-medium"
                onClick={handleSeeMore}
              >
                See more
              </Button>
            </div>
          </div>
        </div>

        {/* Brands Grid */}
        <div
          id="brands-grid"
          className="flex gap-3 sm:gap-4 lg:gap-6 overflow-x-auto scrollbar-hide pb-4"
          style={{
            scrollbarWidth: 'none',
            msOverflowStyle: 'none',
            scrollSnapType: 'x mandatory',
            WebkitOverflowScrolling: 'touch'
          }}
        >
          {brands.map((brand) => (
            <div
              key={brand.id}
              onClick={() => handleBrandClick(brand.name)}
              className="flex-shrink-0 w-28 xs:w-32 sm:w-36 lg:w-40 cursor-pointer group touch-manipulation"
              style={{ scrollSnapAlign: 'start' }}
            >
              <div className="flex flex-col items-center text-center p-2 sm:p-3 lg:p-4 rounded-lg hover:bg-gray-50 active:bg-gray-100 transition-colors duration-200 min-h-[100px] sm:min-h-[110px] lg:min-h-[120px]">
                {/* Brand Logo or Icon */}
                <div className="mb-2 sm:mb-3 text-gray-600 group-hover:text-teal-600 group-active:text-teal-700 transition-colors duration-200">
                  {brand.logo ? (
                    <div className="w-10 h-10 sm:w-11 sm:h-11 lg:w-12 lg:h-12 flex items-center justify-center bg-white rounded-lg border border-gray-100 shadow-sm">
                      <img
                        src={brand.logo}
                        alt={`${brand.name} logo`}
                        className="max-w-full max-h-full object-contain rounded-md"
                        loading="lazy"
                        onError={(e) => {
                          // Fallback to icon if image fails to load
                          const target = e.target as HTMLImageElement;
                          target.style.display = 'none';
                          const iconContainer = target.parentElement;
                          if (iconContainer) {
                            iconContainer.innerHTML = '';
                            const iconDiv = document.createElement('div');
                            iconDiv.className = 'text-gray-600 group-hover:text-teal-600 transition-colors duration-200';
                            iconContainer.appendChild(iconDiv);
                          }
                        }}
                      />
                    </div>
                  ) : (
                    <div className="text-gray-600 group-hover:text-teal-600 group-active:text-teal-700 transition-colors duration-200 [&>svg]:w-6 [&>svg]:h-6 sm:[&>svg]:w-7 sm:[&>svg]:h-7 lg:[&>svg]:w-8 lg:[&>svg]:h-8">
                      {brand.icon}
                    </div>
                  )}
                </div>

                {/* Brand Name */}
                <h3 className="text-xs sm:text-sm font-medium text-gray-900 group-hover:text-teal-600 group-active:text-teal-700 transition-colors duration-200 leading-tight line-clamp-2 text-center">
                  {brand.name}
                </h3>
              </div>
            </div>
          ))}
        </div>


      </div>
    </section>
  );
};

export default ProductBrandsGrid;
