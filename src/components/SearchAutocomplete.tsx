import React, { useState, useEffect, useRef } from 'react';
import { useNavigate } from 'react-router-dom';
import { getSearchSuggestions, SearchSuggestion } from '../services/newApi';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList
} from '@/components/ui/command';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { Search, Tag, Box, Loader2 } from 'lucide-react';
import { cn } from '@/lib/utils';

interface SearchAutocompleteProps {
  placeholder?: string;
  className?: string;
  onSearch?: (query: string) => void;
  autoFocus?: boolean;
}

const SearchAutocomplete: React.FC<SearchAutocompleteProps> = ({
  placeholder = 'Search products...',
  className,
  onSearch,
  autoFocus = false
}) => {
  const [open, setOpen] = useState(false);
  const [query, setQuery] = useState('');
  const [suggestions, setSuggestions] = useState<SearchSuggestion[]>([]);
  const [loading, setLoading] = useState(false);
  const navigate = useNavigate();
  const inputRef = useRef<HTMLInputElement>(null);
  const debounceTimerRef = useRef<NodeJS.Timeout | null>(null);

  // Fetch suggestions when query changes
  useEffect(() => {
    if (query.trim().length < 2) {
      setSuggestions([]);
      return;
    }

    // Debounce the search to avoid too many requests
    if (debounceTimerRef.current) {
      clearTimeout(debounceTimerRef.current);
    }

    const fetchSuggestions = async () => {
      setLoading(true);
      try {
        if (import.meta.env.DEV) {
          console.log('Fetching suggestions for query:', query);
        }
        const results = await getSearchSuggestions(query);
        if (import.meta.env.DEV) {
          console.log('Received suggestions:', results);
        }
        setSuggestions(results);
      } catch (error) {
        if (import.meta.env.DEV) {
          console.error('Error fetching suggestions:', error);
        }
      } finally {
        setLoading(false);
      }
    };

    debounceTimerRef.current = setTimeout(fetchSuggestions, 300);

    return () => {
      if (debounceTimerRef.current) {
        clearTimeout(debounceTimerRef.current);
      }
    };
  }, [query]);

  // Handle form submission
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (query.trim()) {
      if (onSearch) {
        onSearch(query);
      } else {
        navigate(`/search?q=${encodeURIComponent(query)}`);
      }
      setOpen(false);
    }
  };

  // Handle suggestion click
  const handleSuggestionClick = (suggestion: SearchSuggestion) => {
    setOpen(false);

    switch (suggestion.type) {
      case 'product':
        if (suggestion.slug) {
          navigate(`/products/${suggestion.slug}`);
        } else {
          setQuery(suggestion.text);
          if (onSearch) onSearch(suggestion.text);
          else navigate(`/search?q=${encodeURIComponent(suggestion.text)}`);
        }
        break;
      case 'brand':
        navigate(`/search?brand=${encodeURIComponent(suggestion.text)}`);
        break;
      case 'category':
        navigate(`/search?category=${encodeURIComponent(suggestion.text)}`);
        break;
      default:
        setQuery(suggestion.text);
        if (onSearch) onSearch(suggestion.text);
        else navigate(`/search?q=${encodeURIComponent(suggestion.text)}`);
    }
  };

  // Get icon for suggestion type
  const getSuggestionIcon = (type: string) => {
    switch (type) {
      case 'brand':
        return <Tag className="mr-2 h-4 w-4" />;
      case 'category':
        return <Box className="mr-2 h-4 w-4" />;
      default:
        return <Search className="mr-2 h-4 w-4" />;
    }
  };

  return (
    <form onSubmit={handleSubmit} className={cn("relative", className)}>
      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <div className="relative">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              ref={inputRef}
              type="search"
              placeholder={placeholder}
              className="pl-8 pr-10"
              value={query}
              onChange={(e) => setQuery(e.target.value)}
              onClick={() => setOpen(true)}
              autoFocus={autoFocus}
            />
            <Button
              type="submit"
              size="sm"
              variant="ghost"
              className="absolute right-0 top-0 h-full px-3"
              aria-label="Search"
            >
              <Search className="h-4 w-4" />
            </Button>
          </div>
        </PopoverTrigger>
        <PopoverContent
          className="p-0 w-[var(--radix-popover-trigger-width)]"
          align="start"
          side="bottom"
          sideOffset={5}
        >
          <Command>
            <CommandList>
              {loading ? (
                <div className="flex items-center justify-center p-4">
                  <Loader2 className="h-4 w-4 animate-spin mr-2" />
                  <span>Searching...</span>
                </div>
              ) : (
                <>
                  <CommandEmpty>No results found</CommandEmpty>
                  {suggestions.length > 0 && (
                    <CommandGroup heading="Suggestions">
                      {suggestions.map((suggestion, index) => (
                        <CommandItem
                          key={`${suggestion.type}-${index}`}
                          onSelect={() => handleSuggestionClick(suggestion)}
                          className="flex items-center"
                        >
                          {getSuggestionIcon(suggestion.type)}
                          <span>{suggestion.text}</span>
                          <span className="ml-auto text-xs text-muted-foreground">
                            {suggestion.type === 'product' ? 'Product' :
                              suggestion.type === 'brand' ? 'Brand' : 'Category'}
                          </span>
                        </CommandItem>
                      ))}
                    </CommandGroup>
                  )}
                </>
              )}
            </CommandList>
          </Command>
        </PopoverContent>
      </Popover>
    </form>
  );
};

export default SearchAutocomplete;
