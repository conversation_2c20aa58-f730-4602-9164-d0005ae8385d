import React from 'react';
import { Helmet } from 'react-helmet-async';

interface SEOHeadProps {
  title?: string;
  description?: string;
  keywords?: string[];
  image?: string;
  url?: string;
  type?: 'website' | 'article' | 'product';
  author?: string;
  publishedTime?: string;
  modifiedTime?: string;
  category?: string;
  tags?: string[];
  price?: number;
  currency?: string;
  availability?: 'in stock' | 'out of stock' | 'preorder';
  brand?: string;
  productId?: string;
  rating?: number;
  reviewCount?: number;
  noIndex?: boolean;
  canonicalUrl?: string;
}

const SEOHead: React.FC<SEOHeadProps> = ({
  title = 'Halocritique - Honest Product Reviews & Comparisons',
  description = 'Find honest, unbiased product reviews and comparisons. Make informed purchasing decisions with expert-curated opinions and detailed analysis.',
  keywords = ['product reviews', 'product comparisons', 'honest reviews', 'buying guide', 'product opinions'],
  image = '/og-image.jpg',
  url,
  type = 'website',
  author = 'Halocritique Team',
  publishedTime,
  modifiedTime,
  category,
  tags = [],
  price,
  currency = 'USD',
  availability,
  brand,
  productId,
  rating,
  reviewCount,
  noIndex = false,
  canonicalUrl
}) => {
  const siteUrl = typeof window !== 'undefined' ? window.location.origin : 'https://halocritique.com';
  const currentUrl = url || (typeof window !== 'undefined' ? window.location.href : siteUrl);
  const imageUrl = image.startsWith('http') ? image : `${siteUrl}${image}`;
  const canonical = canonicalUrl || currentUrl;

  // Generate structured data
  const generateStructuredData = () => {
    const baseData = {
      '@context': 'https://schema.org',
      '@type': type === 'product' ? 'Product' : 'WebSite',
      name: title,
      description,
      url: currentUrl,
      image: imageUrl,
      author: {
        '@type': 'Organization',
        name: 'Halocritique',
        url: siteUrl
      }
    };

    if (type === 'website') {
      return {
        ...baseData,
        '@type': 'WebSite',
        potentialAction: {
          '@type': 'SearchAction',
          target: `${siteUrl}/search?q={search_term_string}`,
          'query-input': 'required name=search_term_string'
        }
      };
    }

    if (type === 'product') {
      const productData = {
        ...baseData,
        '@type': 'Product',
        brand: brand ? { '@type': 'Brand', name: brand } : undefined,
        category,
        sku: productId,
        aggregateRating: rating && reviewCount ? {
          '@type': 'AggregateRating',
          ratingValue: rating,
          reviewCount,
          bestRating: 5,
          worstRating: 1
        } : undefined,
        offers: price ? {
          '@type': 'Offer',
          price,
          priceCurrency: currency,
          availability: availability === 'in stock' ? 'https://schema.org/InStock' : 
                      availability === 'out of stock' ? 'https://schema.org/OutOfStock' : 
                      'https://schema.org/PreOrder'
        } : undefined
      };

      // Remove undefined properties
      return Object.fromEntries(
        Object.entries(productData).filter(([_, value]) => value !== undefined)
      );
    }

    if (type === 'article') {
      return {
        ...baseData,
        '@type': 'Article',
        headline: title,
        datePublished: publishedTime,
        dateModified: modifiedTime || publishedTime,
        articleSection: category,
        keywords: [...keywords, ...tags].join(', ')
      };
    }

    return baseData;
  };

  return (
    <Helmet>
      {/* Basic Meta Tags */}
      <title>{title}</title>
      <meta name="description" content={description} />
      <meta name="keywords" content={keywords.join(', ')} />
      <meta name="author" content={author} />
      
      {/* Canonical URL */}
      <link rel="canonical" href={canonical} />
      
      {/* Robots */}
      {noIndex && <meta name="robots" content="noindex, nofollow" />}
      
      {/* Open Graph */}
      <meta property="og:type" content={type} />
      <meta property="og:title" content={title} />
      <meta property="og:description" content={description} />
      <meta property="og:image" content={imageUrl} />
      <meta property="og:url" content={currentUrl} />
      <meta property="og:site_name" content="Halocritique" />
      
      {/* Twitter Card */}
      <meta name="twitter:card" content="summary_large_image" />
      <meta name="twitter:title" content={title} />
      <meta name="twitter:description" content={description} />
      <meta name="twitter:image" content={imageUrl} />
      
      {/* Article specific */}
      {type === 'article' && publishedTime && (
        <meta property="article:published_time" content={publishedTime} />
      )}
      {type === 'article' && modifiedTime && (
        <meta property="article:modified_time" content={modifiedTime} />
      )}
      {type === 'article' && category && (
        <meta property="article:section" content={category} />
      )}
      {type === 'article' && tags.map(tag => (
        <meta key={tag} property="article:tag" content={tag} />
      ))}
      
      {/* Product specific */}
      {type === 'product' && price && (
        <>
          <meta property="product:price:amount" content={price.toString()} />
          <meta property="product:price:currency" content={currency} />
        </>
      )}
      {type === 'product' && availability && (
        <meta property="product:availability" content={availability} />
      )}
      
      {/* Structured Data */}
      <script type="application/ld+json">
        {JSON.stringify(generateStructuredData(), null, 2)}
      </script>
    </Helmet>
  );
};

export default SEOHead;
