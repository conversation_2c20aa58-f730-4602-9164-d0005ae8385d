import React, { useState, useEffect } from 'react';
import { createProduct, updateProduct, getCategories, getSubcategories, getBrands, searchBrands } from '../services/newApi';
import FormError from './ui/form-error';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Loader2, Plus, Save, X } from 'lucide-react';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

interface ProductFormProps {
  product?: any;
  onSuccess: () => void;
}

const ProductForm: React.FC<ProductFormProps> = ({ product, onSuccess }) => {
  const [formData, setFormData] = useState({
    name: '',
    category: '',
    subcategory: '',
    brand: '',
    image: '',
    description: '',
    pros: [''],
    cons: [''],
    sources: [''],
    score: 0,
    keywords: [''],
    affiliateLink: ''
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [categories, setCategories] = useState<string[]>([]);
  const [subcategories, setSubcategories] = useState<string[]>([]);
  const [brands, setBrands] = useState<Array<{ name: string, logo?: string, category?: string }>>([]);
  const [loadingCategories, setLoadingCategories] = useState(false);
  const [loadingSubcategories, setLoadingSubcategories] = useState(false);
  const [loadingBrands, setLoadingBrands] = useState(false);

  // Validation errors
  const [validationErrors, setValidationErrors] = useState<{
    [key: string]: string;
  }>({});

  // Fetch categories and brands when component mounts
  useEffect(() => {
    const fetchData = async () => {
      // Fetch categories (optional for backward compatibility)
      setLoadingCategories(true);
      try {
        const categoriesData = await getCategories();
        setCategories(categoriesData);
      } catch (err) {
        console.error('Error fetching categories:', err);
        setError('Failed to load categories');
      } finally {
        setLoadingCategories(false);
      }

      // Fetch brands (primary organization method)
      setLoadingBrands(true);
      try {
        const brandsData = await getBrands();
        setBrands(brandsData);
      } catch (err) {
        console.error('Error fetching brands:', err);
        setError('Failed to load brands');
      } finally {
        setLoadingBrands(false);
      }
    };

    fetchData();
  }, []);

  // Set form data when product changes
  useEffect(() => {
    if (product) {
      if (import.meta.env.DEV) {
        console.log('Setting form data for product:', product);
      }
      setFormData({
        name: product.name || '',
        category: product.category || '',
        subcategory: product.subcategory || '',
        brand: product.brand || '',
        image: product.image || '',
        description: product.description || '',
        pros: product.pros && product.pros.length > 0 ? product.pros : [''],
        cons: product.cons && product.cons.length > 0 ? product.cons : [''],
        sources: product.sources && product.sources.length > 0 ? product.sources : [''],
        score: product.score || '',
        keywords: product.keywords && product.keywords.length > 0 ? product.keywords : [''],
        affiliateLink: product.affiliateLink || ''
      });
      // Clear validation errors when editing a product
      setValidationErrors({});
      if (import.meta.env.DEV) {
        console.log('Form data set with category:', product.category, 'subcategory:', product.subcategory);
      }
    } else {
      // Reset form when no product is being edited
      setFormData({
        name: '',
        category: '',
        subcategory: '',
        brand: '',
        image: '',
        description: '',
        pros: [''],
        cons: [''],
        sources: [''],
        score: 0,
        keywords: [''],
        affiliateLink: ''
      });
      setSubcategories([]);
      setValidationErrors({});
    }
  }, [product]);

  // Fetch subcategories when category changes
  useEffect(() => {
    const fetchSubcategories = async () => {
      if (!formData.category) {
        setSubcategories([]);
        return;
      }

      setLoadingSubcategories(true);
      try {
        const subcategoriesData = await getSubcategories(formData.category);
        console.log('Fetched subcategories for category:', formData.category, subcategoriesData);

        // If editing a product and the current subcategory is not in the fetched list, add it
        let finalSubcategories = [...subcategoriesData];
        if (product && formData.subcategory && !subcategoriesData.includes(formData.subcategory)) {
          console.log('Adding current subcategory to list:', formData.subcategory);
          finalSubcategories = [formData.subcategory, ...subcategoriesData];
        }

        setSubcategories(finalSubcategories);

        // Clear subcategory validation error when subcategories are loaded and we're editing
        if (product && formData.subcategory) {
          setValidationErrors(prev => ({ ...prev, subcategory: '' }));
        }

        // Only reset subcategory if it's not valid for the new category AND we're not editing an existing product
        if (formData.subcategory && !subcategoriesData.includes(formData.subcategory) && !product) {
          console.log('Resetting subcategory because it\'s not valid for new category');
          setFormData(prev => ({ ...prev, subcategory: '' }));
          setValidationErrors(prev => ({ ...prev, subcategory: '' }));
        }
      } catch (err) {
        console.error('Error fetching subcategories:', err);
        setSubcategories([]);
      } finally {
        setLoadingSubcategories(false);
      }
    };

    fetchSubcategories();
  }, [formData.category, product, formData.subcategory]);

  const validateField = (name: string, value: string | number | string[]) => {
    let error = '';

    switch (name) {
      case 'name':
        if (!value) error = 'Product name is required';
        else if (typeof value === 'string' && value.length > 100) error = 'Name cannot be more than 100 characters';
        break;
      case 'category':
        // Category is now optional for brand-based organization
        if (value && typeof value === 'string' && value.trim() === '') error = 'Category cannot be empty if provided';
        break;
      case 'subcategory':
        // Subcategory is now optional for brand-based organization
        if (value && typeof value === 'string' && value.trim() === '') error = 'Subcategory cannot be empty if provided';
        // When editing a product, don't validate subcategory against the list until subcategories are loaded
        else if (!product && value && subcategories.length > 0 && !subcategories.includes(value as string)) {
          error = 'Please select a valid subcategory';
        }
        break;
      case 'brand':
        if (!value) error = 'Brand is required';
        break;
      case 'image':
        if (!value) error = 'Image URL is required';
        else if (typeof value === 'string' && !/^https?:\/\/.+/.test(value)) error = 'Please enter a valid URL';
        break;
      case 'description':
        if (!value) error = 'Description is required';
        else if (typeof value === 'string' && value.length > 1000) error = 'Description cannot be more than 1000 characters';
        break;
      case 'sources':
        if (!value || (Array.isArray(value) && value.length === 0)) error = 'At least one source is required';
        else if (Array.isArray(value) && value.some(source => !source.trim())) error = 'All sources must be non-empty';
        break;
      case 'score':
        if (value === undefined || value === null) error = 'Score is required';
        else if (typeof value === 'number' && (value < 0 || value > 100)) error = 'Score must be between 0 and 100';
        else if (typeof value === 'string') {
          const numValue = Number(value);
          if (isNaN(numValue)) error = 'Score must be a number';
          else if (numValue < 0 || numValue > 100) error = 'Score must be between 0 and 100';
        }
        break;
    }

    return error;
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));

    // Validate the field
    const error = validateField(name, value);
    setValidationErrors(prev => ({
      ...prev,
      [name]: error
    }));
  };

  const handleSelectChange = (name: string, value: string) => {
    setFormData(prev => ({ ...prev, [name]: value }));

    // Clear validation error when a value is selected
    if (value) {
      setValidationErrors(prev => ({
        ...prev,
        [name]: ''
      }));
    } else {
      // Validate the field if no value is selected
      const error = validateField(name, value);
      setValidationErrors(prev => ({
        ...prev,
        [name]: error
      }));
    }
  };

  const handleArrayChange = (type: 'pros' | 'cons' | 'keywords' | 'sources', index: number, value: string) => {
    setFormData(prev => {
      const newArray = [...prev[type]];
      newArray[index] = value;
      return { ...prev, [type]: newArray };
    });
  };

  const addArrayItem = (type: 'pros' | 'cons' | 'keywords' | 'sources') => {
    setFormData(prev => ({
      ...prev,
      [type]: [...prev[type], '']
    }));
  };

  const removeArrayItem = (type: 'pros' | 'cons' | 'keywords' | 'sources', index: number) => {
    if (formData[type].length <= 1) return;

    setFormData(prev => {
      const newArray = [...prev[type]];
      newArray.splice(index, 1);
      return { ...prev, [type]: newArray };
    });
  };

  const validateForm = () => {
    const errors: { [key: string]: string } = {};
    // Category and subcategory are now optional for brand-based organization
    const requiredFields = ['name', 'brand', 'image', 'description', 'sources', 'score'];

    // Check required fields
    requiredFields.forEach(field => {
      const value = formData[field as keyof typeof formData];
      const error = validateField(field, value);
      if (error) errors[field] = error;
    });

    // Validate optional category and subcategory if provided
    if (formData.category) {
      const categoryError = validateField('category', formData.category);
      if (categoryError) errors.category = categoryError;
    }

    if (formData.subcategory) {
      const subcategoryError = validateField('subcategory', formData.subcategory);
      if (subcategoryError) errors.subcategory = subcategoryError;
    }

    // Validate image URL
    if (formData.image && !/^https?:\/\/.+/.test(formData.image)) {
      errors.image = 'Please enter a valid URL';
    }

    setValidationErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError(null);
    setSuccess(null);

    // Validate all fields
    if (!validateForm()) {
      // Scroll to the first error
      const firstErrorField = Object.keys(validationErrors)[0];
      const element = document.getElementById(firstErrorField);
      if (element) element.focus();
      return;
    }

    setLoading(true);

    try {
      // Filter out empty pros, cons, keywords, and sources
      const filteredPros = formData.pros.filter(item => item.trim() !== '');
      const filteredCons = formData.cons.filter(item => item.trim() !== '');
      const filteredKeywords = formData.keywords.filter(item => item.trim() !== '');
      const filteredSources = formData.sources.filter(item => item.trim() !== '');

      const productData: any = {
        ...formData,
        pros: filteredPros,
        cons: filteredCons,
        keywords: filteredKeywords,
        sources: filteredSources
      };

      // Remove empty category and subcategory fields to avoid validation errors
      if (!formData.category || formData.category.trim() === '') {
        delete productData.category;
      }
      if (!formData.subcategory || formData.subcategory.trim() === '') {
        delete productData.subcategory;
      }

      if (product) {
        await updateProduct(product._id, productData);
        setSuccess('Product updated successfully');
      } else {
        await createProduct(productData);
        setSuccess('Product created successfully');

        // Reset form after successful creation
        setFormData({
          name: '',
          category: '',
          subcategory: '',
          brand: '',
          image: '',
          description: '',
          pros: [''],
          cons: [''],
          sources: [''],
          score: 0,
          keywords: [''],
          affiliateLink: ''
        });
        setValidationErrors({});
      }

      // Call onSuccess callback after a short delay to show success message
      setTimeout(() => {
        onSuccess();
      }, 1500);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to save product');
      console.error('Error saving product:', err);
    } finally {
      setLoading(false);
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>{product ? 'Edit Product' : 'Add New Product'}</CardTitle>
        <CardDescription>
          {product ? 'Update product information' : 'Create a new product'}
        </CardDescription>
      </CardHeader>
      <CardContent>
        {error && (
          <Alert variant="destructive" className="mb-4">
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        {success && (
          <Alert className="mb-4">
            <AlertDescription>{success}</AlertDescription>
          </Alert>
        )}

        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <label htmlFor="name">Product Name *</label>
              <Input
                id="name"
                name="name"
                value={formData.name}
                onChange={handleChange}
                className={validationErrors.name ? 'border-destructive' : ''}
                required
              />
              <FormError message={validationErrors.name} />
            </div>

            <div className="space-y-2">
              <label htmlFor="brand">Brand * (Primary Organization)</label>
              <Select
                value={formData.brand}
                onValueChange={(value) => handleSelectChange('brand', value)}
                required
              >
                <SelectTrigger id="brand" className={validationErrors.brand ? 'border-destructive' : ''}>
                  <SelectValue placeholder="Select a brand" />
                </SelectTrigger>
                <SelectContent className="max-h-[200px] overflow-y-auto">
                  {loadingBrands ? (
                    <div className="flex items-center justify-center p-2">
                      <Loader2 className="h-4 w-4 animate-spin mr-2" /> Loading...
                    </div>
                  ) : brands.length > 0 ? (
                    brands.map((brand) => (
                      <SelectItem key={brand.name} value={brand.name}>
                        {brand.name}
                      </SelectItem>
                    ))
                  ) : (
                    <div className="p-2 text-center text-muted-foreground">
                      No brands found. Please add brands in admin settings.
                    </div>
                  )}
                </SelectContent>
              </Select>
              <FormError message={validationErrors.brand} />
              <p className="text-xs text-muted-foreground">
                Select from available brands. To add new brands, go to Admin → Brand Management.
              </p>
            </div>

            <div className="space-y-2">
              <label htmlFor="category">Category (Optional)</label>
              <Select
                value={formData.category}
                onValueChange={(value) => handleSelectChange('category', value)}
              >
                <SelectTrigger id="category" className={validationErrors.category ? 'border-destructive' : ''}>
                  <SelectValue placeholder="Select a category" />
                </SelectTrigger>
                <SelectContent className="max-h-[200px] overflow-y-auto">
                  {loadingCategories ? (
                    <div className="flex items-center justify-center p-2">
                      <Loader2 className="h-4 w-4 animate-spin mr-2" /> Loading...
                    </div>
                  ) : categories.length > 0 ? (
                    categories.map((category) => (
                      <SelectItem key={category} value={category}>
                        {category}
                      </SelectItem>
                    ))
                  ) : (
                    <div className="p-2 text-center text-muted-foreground">
                      No categories found
                    </div>
                  )}
                </SelectContent>
              </Select>
              <FormError message={validationErrors.category} />
            </div>

            <div className="space-y-2">
              <label htmlFor="subcategory">Subcategory (Optional)</label>
              <Select
                value={formData.subcategory}
                onValueChange={(value) => handleSelectChange('subcategory', value)}
                disabled={!formData.category}
              >
                <SelectTrigger id="subcategory" className={validationErrors.subcategory ? 'border-destructive' : ''}>
                  <SelectValue placeholder={formData.category ? "Select a subcategory" : "Select a category first"} />
                </SelectTrigger>
                <SelectContent className="max-h-[200px] overflow-y-auto">
                  {loadingSubcategories ? (
                    <div className="flex items-center justify-center p-2">
                      <Loader2 className="h-4 w-4 animate-spin mr-2" /> Loading...
                    </div>
                  ) : subcategories.length > 0 ? (
                    subcategories.map((subcategory, index) => (
                      <SelectItem key={subcategory} value={subcategory}>
                        {subcategory}
                        {product && formData.subcategory === subcategory && index === 0 ? ' (current)' : ''}
                      </SelectItem>
                    ))
                  ) : formData.category ? (
                    <div className="p-2 text-center text-muted-foreground">
                      No subcategories found
                    </div>
                  ) : (
                    <div className="p-2 text-center text-muted-foreground">
                      Select a category first
                    </div>
                  )}
                </SelectContent>
              </Select>
              <FormError message={validationErrors.subcategory} />
            </div>

            {/* Price field removed as per terms and conditions */}

            <div className="space-y-2 md:col-span-2">
              <label htmlFor="image">Image URL *</label>
              <Input
                id="image"
                name="image"
                value={formData.image}
                onChange={handleChange}
                className={validationErrors.image ? 'border-destructive' : ''}
                required
              />
              <FormError message={validationErrors.image} />
            </div>

            <div className="space-y-2 md:col-span-2">
              <div className="flex justify-between">
                <label htmlFor="description">Description *</label>
                <span className={`text-xs ${formData.description.length > 1000 ? 'text-destructive' : 'text-muted-foreground'}`}>
                  {formData.description.length}/1000 characters
                </span>
              </div>
              <Textarea
                id="description"
                name="description"
                value={formData.description}
                onChange={handleChange}
                className={validationErrors.description ? 'border-destructive' : ''}
                rows={4}
                required
              />
              <FormError message={validationErrors.description} />
              <p className="text-xs text-muted-foreground">Provide a detailed description of the product (up to 1000 characters)</p>
            </div>

            <div className="space-y-2 md:col-span-2">
              <label>Pros</label>
              {formData.pros.map((pro, index) => (
                <div key={`pro-${index}`} className="flex gap-2 mb-2">
                  <Input
                    value={pro}
                    onChange={(e) => handleArrayChange('pros', index, e.target.value)}
                    placeholder={`Pro ${index + 1}`}
                  />
                  <Button
                    type="button"
                    variant="outline"
                    size="icon"
                    onClick={() => removeArrayItem('pros', index)}
                  >
                    <X className="h-4 w-4" />
                  </Button>
                </div>
              ))}
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={() => addArrayItem('pros')}
              >
                <Plus className="h-4 w-4 mr-2" /> Add Pro
              </Button>
            </div>

            <div className="space-y-2 md:col-span-2">
              <label>Cons</label>
              {formData.cons.map((con, index) => (
                <div key={`con-${index}`} className="flex gap-2 mb-2">
                  <Input
                    value={con}
                    onChange={(e) => handleArrayChange('cons', index, e.target.value)}
                    placeholder={`Con ${index + 1}`}
                  />
                  <Button
                    type="button"
                    variant="outline"
                    size="icon"
                    onClick={() => removeArrayItem('cons', index)}
                  >
                    <X className="h-4 w-4" />
                  </Button>
                </div>
              ))}
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={() => addArrayItem('cons')}
              >
                <Plus className="h-4 w-4 mr-2" /> Add Con
              </Button>
            </div>

            <div className="space-y-2 md:col-span-2">
              <label>Sources *</label>
              <div className="space-y-2">
                {formData.sources.map((source, index) => (
                  <div key={index} className="flex gap-2">
                    <Input
                      value={source}
                      onChange={(e) => handleArrayChange('sources', index, e.target.value)}
                      placeholder="Enter source URL or description"
                      className={validationErrors.sources ? 'border-destructive' : ''}
                    />
                    {formData.sources.length > 1 && (
                      <Button
                        type="button"
                        variant="outline"
                        size="sm"
                        onClick={() => removeArrayItem('sources', index)}
                      >
                        <X className="h-4 w-4" />
                      </Button>
                    )}
                  </div>
                ))}
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={() => addArrayItem('sources')}
                  className="w-full"
                >
                  <Plus className="h-4 w-4 mr-2" />
                  Add Source
                </Button>
              </div>
              <FormError message={validationErrors.sources} />
              <p className="text-xs text-muted-foreground">Add trusted sources or links where this product information comes from</p>
            </div>

            <div className="space-y-2">
              <label htmlFor="score">Score (0-100%) *</label>
              <Input
                id="score"
                name="score"
                type="number"
                min="0"
                max="100"
                value={formData.score}
                onChange={handleChange}
                className={validationErrors.score ? 'border-destructive' : ''}
                required
              />
              <FormError message={validationErrors.score} />
              <p className="text-xs text-muted-foreground">Enter a percentage score between 0 and 100</p>
            </div>

            <div className="space-y-2 md:col-span-2">
              <label>Keywords</label>
              <p className="text-xs text-muted-foreground mb-2">Add keywords to make this product easier to find in searches (e.g., "wireless", "bluetooth", "gaming")</p>
              {formData.keywords.map((keyword, index) => (
                <div key={index} className="flex gap-2">
                  <Input
                    value={keyword}
                    onChange={(e) => handleArrayChange('keywords', index, e.target.value)}
                    placeholder={`Keyword ${index + 1}`}
                    maxLength={50}
                  />
                  <Button
                    type="button"
                    variant="outline"
                    size="icon"
                    onClick={() => removeArrayItem('keywords', index)}
                  >
                    <X className="h-4 w-4" />
                  </Button>
                </div>
              ))}
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={() => addArrayItem('keywords')}
              >
                <Plus className="h-4 w-4 mr-2" /> Add Keyword
              </Button>
            </div>

            <div className="space-y-2">
              <label htmlFor="affiliateLink">Affiliate Link</label>
              <Input
                id="affiliateLink"
                name="affiliateLink"
                value={formData.affiliateLink}
                onChange={handleChange}
                className={validationErrors.affiliateLink ? 'border-destructive' : ''}
              />
              <FormError message={validationErrors.affiliateLink} />
            </div>
          </div>

          <div className="flex justify-end space-x-2 pt-4">
            <Button
              type="button"
              variant="outline"
              onClick={onSuccess}
              disabled={loading}
            >
              Cancel
            </Button>
            <Button type="submit" disabled={loading}>
              {loading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" /> Saving...
                </>
              ) : (
                <>
                  <Save className="mr-2 h-4 w-4" /> {product ? 'Update' : 'Create'}
                </>
              )}
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  );
};

export default ProductForm;
