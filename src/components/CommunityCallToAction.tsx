import React from "react";
import { But<PERSON> } from "@/components/ui/button";
import { ArrowR<PERSON>, Star, Users } from "lucide-react";
import { useNavigate } from "react-router-dom";

const CommunityCallToAction = () => {
  const navigate = useNavigate();

  const handleStartExploring = () => {
    navigate('/brands');
  };

  return (
    <section className="py-20 bg-gray-50">
      <div className="container mx-auto px-4 max-w-5xl">
        {/* Main Content */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-100 p-12 mb-12">
          <div className="text-center max-w-3xl mx-auto">
            <div className="inline-block bg-blue-50 text-blue-700 px-4 py-2 rounded-full text-sm font-medium mb-8">
              Your Shopping Compass
            </div>

            <h2 className="text-5xl font-bold text-gray-900 mb-6 leading-tight">
              Find Your Perfect Match
            </h2>

            <p className="text-xl text-gray-600 mb-10 leading-relaxed">
              Stop endless scrolling. Start smart shopping.<br />
              Halocritique cuts through the noise to show you what really matters.
            </p>

            <div className="flex flex-col sm:flex-row gap-6 justify-center items-center">
              <Button
                size="lg"
                onClick={handleStartExploring}
                className="bg-gray-900 hover:bg-gray-800 text-white px-8 py-4 rounded-lg font-medium text-lg group"
              >
                Start Exploring
                <ArrowRight className="ml-2 h-5 w-5 group-hover:translate-x-1 transition-transform" />
              </Button>

              <div className="flex items-center gap-3 text-gray-500">
                <div className="flex -space-x-1">
                  <div className="w-8 h-8 bg-blue-500 rounded-full border-2 border-white"></div>
                  <div className="w-8 h-8 bg-green-500 rounded-full border-2 border-white"></div>
                  <div className="w-8 h-8 bg-purple-500 rounded-full border-2 border-white"></div>
                  <div className="w-8 h-8 bg-orange-500 rounded-full border-2 border-white"></div>
                </div>
                <span className="text-sm font-medium">50K+ smart shoppers</span>
              </div>
            </div>
          </div>
        </div>

        {/* Feature Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
          {/* Reviews Card */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-100 p-8 hover:shadow-md transition-shadow">
            <div className="flex items-start justify-between mb-6">
              <div className="w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center">
                <Star className="h-6 w-6 text-yellow-600" />
              </div>
              <div className="text-right">
                <div className="text-2xl font-bold text-gray-900">98%</div>
                <div className="text-sm text-gray-500">Accuracy Rate</div>
              </div>
            </div>

            <h3 className="text-2xl font-bold text-gray-900 mb-3">
              Real Reviews, Real Results
            </h3>
            <p className="text-gray-600 mb-6 leading-relaxed">
              Our AI analyzes thousands of reviews to give you the honest truth about every product.
            </p>
            <Button
              variant="outline"
              className="border-gray-200 text-gray-700 hover:bg-gray-50 rounded-lg font-medium"
            >
              See How It Works
            </Button>
          </div>

          {/* Community Card */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-100 p-8 hover:shadow-md transition-shadow">
            <div className="flex items-start justify-between mb-6">
              <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                <Users className="h-6 w-6 text-blue-600" />
              </div>
              <div className="text-right">
                <div className="text-2xl font-bold text-gray-900">50K+</div>
                <div className="text-sm text-gray-500">Active Users</div>
              </div>
            </div>

            <h3 className="text-2xl font-bold text-gray-900 mb-3">
              Join the Movement
            </h3>
            <p className="text-gray-600 mb-6 leading-relaxed">
              Be part of a community that values honest opinions and smart shopping decisions.
            </p>
            <Button
              variant="outline"
              className="border-gray-200 text-gray-700 hover:bg-gray-50 rounded-lg font-medium"
            >
              Get Started
            </Button>
          </div>
        </div>
      </div>
    </section>
  );
};

export default CommunityCallToAction;
