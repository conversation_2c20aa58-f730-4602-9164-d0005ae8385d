import React, { useState } from "react";
import { Menu, X, Star, Home, Building2, Info, Search } from "lucide-react";
import { Link, useNavigate, useLocation } from "react-router-dom";
import { Button } from "@/components/ui/button";
import SimpleSearchAutocomplete from "./SimpleSearchAutocomplete";
import { useScrollDirection } from "../hooks/useScrollDirection";

interface HeaderProps {
  className?: string;
}

const Header: React.FC<HeaderProps> = ({ className = "" }) => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const navigate = useNavigate();
  const location = useLocation();
  const { isVisible } = useScrollDirection({ threshold: 10 });

  // Don't show search bar on homepage
  const isHomePage = location.pathname === '/';

  return (
    <header className={`border-b bg-white sticky top-0 z-50 shadow-sm transition-transform duration-300 ease-in-out ${isVisible ? 'translate-y-0' : '-translate-y-full'
      } ${className}`}>
      <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-3 flex items-center justify-between">
        {/* Logo and mobile menu */}
        <div className="flex items-center space-x-2 sm:space-x-3">
          <Button
            variant="ghost"
            size="icon"
            className="md:hidden relative group touch-manipulation min-h-[44px] min-w-[44px] p-2"
            onClick={() => setIsMenuOpen(!isMenuOpen)}
            aria-label="Toggle menu"
          >
            <div className="relative">
              {isMenuOpen ? (
                <X className="h-6 w-6 text-gray-700 group-hover:text-teal-600 group-active:text-teal-700 transition-colors" />
              ) : (
                <Menu className="h-6 w-6 text-gray-700 group-hover:text-teal-600 group-active:text-teal-700 transition-colors" />
              )}
            </div>
          </Button>

          <Link to="/" className="flex items-center space-x-2 touch-manipulation">
            <Star className="h-5 w-5 sm:h-6 sm:w-6 text-teal-600" />
            <h1 className="text-xl sm:text-2xl lg:text-3xl halocritique-logo">Halocritique</h1>
          </Link>
        </div>

        {/* Search bar - centered on desktop, hidden on mobile and homepage */}
        {!isHomePage && (
          <div className="hidden md:flex flex-1 max-w-xl mx-4 lg:mx-6">
            <SimpleSearchAutocomplete
              placeholder="Find real products opinions..."
              className="w-full"
              onSearch={(query) => {
                if (query.trim()) {
                  navigate(`/search?q=${encodeURIComponent(query)}`);
                }
              }}
            />
          </div>
        )}

        {/* Desktop navigation */}
        <nav className="hidden md:block">
          <ul className="flex space-x-4 lg:space-x-6">
            <li>
              <Link to="/" className="text-sm lg:text-base font-medium hover:text-teal-600 active:text-teal-700 transition-colors touch-manipulation px-2 py-1">
                Home
              </Link>
            </li>
            <li>
              <Link to="/brands" className="text-sm lg:text-base font-medium hover:text-teal-600 active:text-teal-700 transition-colors touch-manipulation px-2 py-1">
                Brands
              </Link>
            </li>
            <li>
              <Link to="/about" className="text-sm lg:text-base font-medium hover:text-teal-600 active:text-teal-700 transition-colors touch-manipulation px-2 py-1">
                About
              </Link>
            </li>
          </ul>
        </nav>

        {/* Mobile menu overlay */}
        {isMenuOpen && (
          <>
            {/* Backdrop */}
            <div
              className="fixed inset-0 z-[9998] md:hidden"
              onClick={() => setIsMenuOpen(false)}
              style={{
                backgroundColor: 'rgba(0, 0, 0, 0.5)',
                backdropFilter: 'blur(4px)',
                WebkitBackdropFilter: 'blur(4px)'
              }}
            />

            {/* Mobile menu panel - Full height with solid white background */}
            <div
              className="fixed top-0 right-0 z-[9999] md:hidden animate-in slide-in-from-right duration-300"
              style={{
                width: 'min(320px, 85vw)',
                height: '100vh',
                backgroundColor: '#ffffff',
                boxShadow: '-8px 0 32px rgba(0, 0, 0, 0.2)',
                overflowY: 'auto',
                WebkitOverflowScrolling: 'touch'
              }}
            >
              {/* Menu header */}
              <div className="flex items-center justify-between p-4 sm:p-6 border-b border-gray-100 bg-white sticky top-0 z-10">
                <div className="flex items-center space-x-2">
                  <Star className="h-5 w-5 sm:h-6 sm:w-6 text-teal-600" />
                  <span className="text-lg sm:text-xl halocritique-logo text-gray-900 font-semibold">Halocritique</span>
                </div>
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={() => setIsMenuOpen(false)}
                  className="h-10 w-10 sm:h-8 sm:w-8 rounded-full hover:bg-gray-100 active:bg-gray-200 text-gray-600 hover:text-gray-900 touch-manipulation"
                  aria-label="Close menu"
                >
                  <X className="h-5 w-5" />
                </Button>
              </div>

              {/* Search bar in mobile menu (only show if not on homepage) */}
              {!isHomePage && (
                <div className="p-6 border-b border-gray-100 bg-white">
                  <SimpleSearchAutocomplete
                    placeholder="Search products..."
                    className="w-full"
                    onSearch={(query) => {
                      if (query.trim()) {
                        navigate(`/search?q=${encodeURIComponent(query)}`);
                        setIsMenuOpen(false);
                      }
                    }}
                  />
                </div>
              )}

              {/* Navigation */}
              <div className="p-4 sm:p-6 bg-white">
                <nav>
                  <ul className="space-y-1 sm:space-y-2">
                    <li>
                      <Link
                        to="/"
                        className={`flex items-center space-x-3 px-4 py-4 rounded-xl text-gray-800 hover:bg-teal-50 hover:text-teal-700 active:bg-teal-100 active:text-teal-800 transition-all duration-200 font-medium touch-manipulation min-h-[56px] ${location.pathname === '/' ? 'bg-teal-50 text-teal-700 shadow-sm' : ''
                          }`}
                        onClick={() => setIsMenuOpen(false)}
                      >
                        <Home className="h-6 w-6 flex-shrink-0" />
                        <span className="text-base sm:text-lg">Home</span>
                      </Link>
                    </li>
                    <li>
                      <Link
                        to="/brands"
                        className={`flex items-center space-x-3 px-4 py-4 rounded-xl text-gray-800 hover:bg-teal-50 hover:text-teal-700 active:bg-teal-100 active:text-teal-800 transition-all duration-200 font-medium touch-manipulation min-h-[56px] ${location.pathname === '/brands' ? 'bg-teal-50 text-teal-700 shadow-sm' : ''
                          }`}
                        onClick={() => setIsMenuOpen(false)}
                      >
                        <Building2 className="h-6 w-6 flex-shrink-0" />
                        <span className="text-base sm:text-lg">Brands</span>
                      </Link>
                    </li>
                    <li>
                      <Link
                        to="/about"
                        className={`flex items-center space-x-3 px-4 py-4 rounded-xl text-gray-800 hover:bg-teal-50 hover:text-teal-700 active:bg-teal-100 active:text-teal-800 transition-all duration-200 font-medium touch-manipulation min-h-[56px] ${location.pathname === '/about' ? 'bg-teal-50 text-teal-700 shadow-sm' : ''
                          }`}
                        onClick={() => setIsMenuOpen(false)}
                      >
                        <Info className="h-6 w-6 flex-shrink-0" />
                        <span className="text-base sm:text-lg">About</span>
                      </Link>
                    </li>
                  </ul>
                </nav>

                {/* Additional menu items */}
                <div className="mt-8 pt-6 border-t border-gray-100">
                  <div className="text-xs font-semibold text-gray-500 uppercase tracking-wider mb-4 px-4">
                    Quick Actions
                  </div>
                  <ul className="space-y-2">
                    <li>
                      <button
                        onClick={() => {
                          navigate('/search');
                          setIsMenuOpen(false);
                        }}
                        className="flex items-center space-x-3 px-4 py-3 rounded-xl text-gray-700 hover:bg-gray-50 hover:text-gray-900 transition-all duration-200 w-full text-left"
                      >
                        <Search className="h-5 w-5" />
                        <span className="text-base">Browse All Products</span>
                      </button>
                    </li>
                  </ul>
                </div>
              </div>

              {/* Menu footer */}
              <div className="absolute bottom-0 left-0 right-0 p-6 border-t border-gray-100 bg-gray-50">
                <div className="text-center">
                  <p className="text-xs text-gray-500 font-medium">
                    © {new Date().getFullYear()} Halocritique. All rights reserved.
                  </p>
                </div>
              </div>
            </div>
          </>
        )}
      </div>

      {/* Mobile search bar - shown below header, hidden on homepage */}
      {!isHomePage && (
        <div className="md:hidden px-4 sm:px-6 py-3 border-t bg-gray-50">
          <SimpleSearchAutocomplete
            placeholder="We’ve done the research so you don’t have to..."
            className="w-full"
            onSearch={(query) => {
              if (query.trim()) {
                navigate(`/search?q=${encodeURIComponent(query)}`);
              }
            }}
          />
        </div>
      )}
    </header>
  );
};

export default Header;
