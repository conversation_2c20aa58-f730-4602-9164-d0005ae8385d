# Halocritique Opinions

Halocritique Opinions is a product comparison and opinion aggregation platform that helps users make informed purchasing decisions by providing comprehensive product information, opinions, and comparisons.

## Features

- Product search and comparison
- Opinion aggregation and sentiment analysis
- Category-based product browsing
- Admin dashboard for product management
- Responsive design for all devices

## Technology Stack

- **Frontend**: React, TypeScript, Tailwind CSS, Shadcn UI
- **Backend**: Node.js, Express
- **Database**: MongoDB
- **Authentication**: JWT

## Getting Started

### Prerequisites

- Node.js (v14 or higher)
- npm (v7 or higher)
- MongoDB (v4 or higher)

### Installation

1. Clone the repository:
   ```bash
   git clone https://github.com/yourusername/halo-opinions.git
   cd halo-opinions
   ```

2. Install dependencies:
   ```bash
   npm install
   ```

3. Set up environment variables:
   - Create a `config.env` file in the root directory
   - Add the following variables:
     ```
     NODE_ENV=development
     PORT=5005
     MONGODB_URI=your_mongodb_connection_string
     JWT_SECRET=your_jwt_secret
     JWT_EXPIRES_IN=30d
     ```

4. Start the development server:
   ```bash
   # Start frontend and backend concurrently
   npm run dev:all

   # Or start them separately
   npm run dev        # Frontend
   npm run server     # Backend
   ```

5. Create admin user and database indexes:
   ```bash
   npm run create-admin
   npm run create-indexes
   ```

## Deployment

For detailed deployment instructions, see [DEPLOYMENT.md](DEPLOYMENT.md).

### Quick Deployment to Render

1. Fork this repository
2. Sign up for [Render](https://render.com)
3. Create a new Web Service and connect your repository
4. Render will automatically detect the `render.yaml` file and configure the service
5. Add your MongoDB connection string as an environment variable
6. Deploy!

## Documentation

- API documentation is available at `/api-docs` when the server is running
- Developer documentation is in the `docs` directory

## License

This project is licensed under the MIT License - see the LICENSE file for details.
