{"name": "halocritique-review-server", "version": "1.0.0", "description": "Backend for Halocritique Review product comparison app", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "echo \"Error: no test specified\" && exit 1", "create-indexes": "node utils/createIndexes.js"}, "dependencies": {"axios": "^1.9.0", "bcryptjs": "^2.4.3", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "csurf": "^1.10.0", "dotenv": "^16.3.1", "express": "^4.18.2", "express-mongo-sanitize": "^2.2.0", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "helmet": "^8.1.0", "jsonwebtoken": "^9.0.2", "mongoose": "^8.0.0", "morgan": "^1.10.0", "node-cache": "^5.1.2", "slugify": "^1.6.6", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.0", "xss-clean": "^0.1.4"}, "devDependencies": {"nodemon": "^3.0.1"}}