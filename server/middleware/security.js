const csrf = require('csurf');
const helmet = require('helmet');
const xssClean = require('xss-clean');
const mongoSanitize = require('express-mongo-sanitize');
const cookieParser = require('cookie-parser');

// Initialize CSRF protection
const csrfProtection = csrf({
  cookie: {
    httpOnly: true,
    secure: process.env.NODE_ENV === 'production',
    sameSite: 'strict'
  }
});

// Generate CSRF token middleware
const generateCsrfToken = (req, res, next) => {
  res.cookie('XSRF-TOKEN', req.csrfToken(), {
    httpOnly: false, // Client-side JavaScript needs to read this
    secure: process.env.NODE_ENV === 'production',
    sameSite: 'strict'
  });
  next();
};

// Content Security Policy configuration
const contentSecurityPolicy = helmet.contentSecurityPolicy({
  directives: {
    defaultSrc: ["'self'"],
    scriptSrc: ["'self'", "'unsafe-inline'", "https://api.tempolabs.ai"],
    styleSrc: ["'self'", "'unsafe-inline'", "https://fonts.googleapis.com"],
    imgSrc: ["'self'", "data:", "https:", "http:"],
    fontSrc: ["'self'", "https://fonts.gstatic.com"],
    connectSrc: ["'self'", "https://api.tempolabs.ai"],
    frameSrc: ["'self'"],
    objectSrc: ["'none'"],
    upgradeInsecureRequests: [],
  },
});

// Configure Helmet with all security headers
const securityHeaders = helmet({
  contentSecurityPolicy: false, // We'll add our custom CSP separately
  xssFilter: true,
  noSniff: true,
  ieNoOpen: true,
  frameguard: {
    action: 'sameorigin'
  },
  hsts: {
    maxAge: 31536000, // 1 year in seconds
    includeSubDomains: true,
    preload: true
  },
  referrerPolicy: { policy: 'same-origin' }
});

// XSS protection middleware
const xssProtection = xssClean();

// MongoDB query sanitization to prevent NoSQL injection
const mongoSanitization = mongoSanitize({
  replaceWith: '_'
});

// Apply all security middleware
const applySecurityMiddleware = (app) => {
  // Parse cookies for CSRF
  app.use(cookieParser());

  // Apply security headers
  app.use(securityHeaders);
  app.use(contentSecurityPolicy);

  // Sanitize data
  app.use(xssProtection);
  app.use(mongoSanitization);

  // Temporarily disable CSRF protection for development/testing
  if (process.env.NODE_ENV !== 'production') {
    console.log('CSRF protection is temporarily disabled for development/testing');
  }

  // Add a simple middleware to handle CSRF token in responses
  app.use((req, res, next) => {
    const originalJson = res.json;
    res.json = function(body) {
      if (body && typeof body === 'object') {
        body.csrfToken = 'csrf-disabled-for-testing';
      }
      return originalJson.call(this, body);
    };
    next();
  });
};

module.exports = {
  applySecurityMiddleware,
  csrfProtection,
  generateCsrfToken
};
