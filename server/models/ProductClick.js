const mongoose = require('mongoose');

/**
 * ProductClick Schema
 * Used to track product clicks for analytics
 */
const ProductClickSchema = new mongoose.Schema({
  productId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Product',
    required: true
  },
  userId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    default: null
  },
  sessionId: {
    type: String,
    default: null
  },
  ipAddress: {
    type: String,
    default: null
  },
  userAgent: {
    type: String,
    default: null
  },
  referrer: {
    type: String,
    default: null
  },
  timestamp: {
    type: Date,
    default: Date.now
  }
});

// Create indexes for efficient querying
ProductClickSchema.index({ productId: 1 });
ProductClickSchema.index({ userId: 1 });
ProductClickSchema.index({ sessionId: 1 });
ProductClickSchema.index({ timestamp: -1 });

// Create compound index for product and time
ProductClickSchema.index({ productId: 1, timestamp: -1 });

/**
 * Static method to get click count for a product
 * @param {string} productId - Product ID
 * @returns {Promise<number>} - Number of clicks
 */
ProductClickSchema.statics.getClickCount = async function(productId) {
  return this.countDocuments({ productId });
};

/**
 * Static method to get click counts for multiple products
 * @param {Array<string>} productIds - Array of product IDs
 * @returns {Promise<Object>} - Object with product IDs as keys and click counts as values
 */
ProductClickSchema.statics.getClickCounts = async function(productIds) {
  const results = await this.aggregate([
    { $match: { productId: { $in: productIds.map(id => mongoose.Types.ObjectId(id)) } } },
    { $group: { _id: '$productId', count: { $sum: 1 } } }
  ]);

  // Convert to object with product IDs as keys
  const counts = {};
  results.forEach(result => {
    counts[result._id.toString()] = result.count;
  });

  return counts;
};



module.exports = mongoose.model('ProductClick', ProductClickSchema);
