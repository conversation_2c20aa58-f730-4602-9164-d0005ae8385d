const express = require('express');
const { generateSitemap, generateRobotsTxt, getStructuredData } = require('../controllers/sitemap.controller');
const { setCacheHeaders } = require('../utils/cache');

const router = express.Router();

// Add cache headers middleware
const addCacheHeaders = (maxAge) => (req, res, next) => {
  setCacheHeaders(res, maxAge);
  next();
};

/**
 * @swagger
 * tags:
 *   name: SEO
 *   description: SEO and sitemap endpoints
 */

/**
 * @swagger
 * /sitemap.xml:
 *   get:
 *     summary: Generate XML sitemap
 *     tags: [SEO]
 *     responses:
 *       200:
 *         description: XML sitemap
 *         content:
 *           application/xml:
 *             schema:
 *               type: string
 *       500:
 *         description: Server error
 */
router.get('/sitemap.xml', addCacheHeaders(3600), generateSitemap);

/**
 * @swagger
 * /robots.txt:
 *   get:
 *     summary: Generate robots.txt
 *     tags: [SEO]
 *     responses:
 *       200:
 *         description: Robots.txt file
 *         content:
 *           text/plain:
 *             schema:
 *               type: string
 *       500:
 *         description: Server error
 */
router.get('/robots.txt', addCacheHeaders(86400), generateRobotsTxt);

/**
 * @swagger
 * /structured-data:
 *   get:
 *     summary: Get structured data for homepage
 *     tags: [SEO]
 *     responses:
 *       200:
 *         description: Structured data JSON-LD
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *       500:
 *         description: Server error
 */
router.get('/structured-data', addCacheHeaders(3600), getStructuredData);

module.exports = router;
