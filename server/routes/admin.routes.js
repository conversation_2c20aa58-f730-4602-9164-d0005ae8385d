const express = require('express');
const { protect, authorize } = require('../middleware/auth');

const router = express.Router();

// Protect all routes in this router
router.use(protect);
router.use(authorize('admin'));

// Admin dashboard route
router.get('/dashboard', (req, res) => {
  res.status(200).json({
    success: true,
    message: 'Admin dashboard'
  });
});

// Brand management routes
router.use('/brands', require('./admin.brand.routes'));

module.exports = router;
