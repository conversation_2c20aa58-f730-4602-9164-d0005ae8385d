const express = require('express');
const {
  getBrands,
  getBrandsWithStats,
  getBrandProducts,
  getBrandStats,
  searchBrands
} = require('../controllers/brand.controller');

const router = express.Router();

// Search brands (must be before /:brand routes)
router.get('/search', searchBrands);

// Get all brands
router.get('/', getBrands);

// Get brands with statistics
router.get('/with-stats', getBrandsWithStats);

// Get products for a specific brand
router.get('/:brand/products', getBrandProducts);

// Get statistics for a specific brand
router.get('/:brand/stats', getBrandStats);

module.exports = router;
