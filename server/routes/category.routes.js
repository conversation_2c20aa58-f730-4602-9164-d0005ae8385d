const express = require('express');
const {
  getCategories,
  getCategoriesWithSubcategories,
  getSubcategories
} = require('../controllers/category.controller');

const router = express.Router();

// Get all categories
router.get('/', getCategories);

// Get all categories with their subcategories
router.get('/with-subcategories', getCategoriesWithSubcategories);

// Get subcategories for a specific category
router.get('/:category/subcategories', getSubcategories);

// Score is now a percentage (0-100) instead of predefined categories

module.exports = router;
