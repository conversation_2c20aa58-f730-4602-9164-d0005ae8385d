const express = require('express');
const { body } = require('express-validator');
const {
  getAdminBrands,
  createBrand,
  updateBrand,
  deleteBrand,
  getBrandById,
  toggleBrandStatus
} = require('../controllers/admin.brand.controller');
const { protect, authorize } = require('../middleware/auth');

const router = express.Router();

// All routes require admin authentication
router.use(protect);
router.use(authorize('admin'));

/**
 * @swagger
 * components:
 *   schemas:
 *     Brand:
 *       type: object
 *       required:
 *         - name
 *       properties:
 *         _id:
 *           type: string
 *           description: The auto-generated ID of the brand
 *         name:
 *           type: string
 *           description: Brand name
 *           maxLength: 50
 *         slug:
 *           type: string
 *           description: URL-friendly version of the name
 *         description:
 *           type: string
 *           description: Brand description
 *           maxLength: 500
 *         logo:
 *           type: string
 *           description: URL to brand logo
 *         website:
 *           type: string
 *           description: Brand website URL
 *         category:
 *           type: string
 *           description: Brand category
 *           enum: [Technology, Fashion, Automotive, Gaming, Beauty, Home & Kitchen, Sports, Health, Other]
 *         isActive:
 *           type: boolean
 *           description: Whether the brand is active
 *         productCount:
 *           type: integer
 *           description: Number of products for this brand
 *         createdAt:
 *           type: string
 *           format: date-time
 *           description: Date when the brand was created
 *         updatedAt:
 *           type: string
 *           format: date-time
 *           description: Date when the brand was last updated
 */

/**
 * @swagger
 * /admin/brands:
 *   get:
 *     summary: Get all brands (admin only)
 *     description: Retrieve all brands with pagination and filtering
 *     tags: [Admin - Brands]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *         description: Page number
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 10
 *         description: Number of brands per page
 *       - in: query
 *         name: search
 *         schema:
 *           type: string
 *         description: Search term for brand name or description
 *       - in: query
 *         name: category
 *         schema:
 *           type: string
 *         description: Filter by brand category
 *       - in: query
 *         name: isActive
 *         schema:
 *           type: boolean
 *         description: Filter by active status
 *     responses:
 *       200:
 *         description: Brands retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 count:
 *                   type: integer
 *                 pagination:
 *                   type: object
 *                   properties:
 *                     page:
 *                       type: integer
 *                     limit:
 *                       type: integer
 *                     totalPages:
 *                       type: integer
 *                     total:
 *                       type: integer
 *                 data:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/Brand'
 */
router.get('/', getAdminBrands);

/**
 * @swagger
 * /admin/brands:
 *   post:
 *     summary: Create a new brand (admin only)
 *     description: Create a new brand
 *     tags: [Admin - Brands]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - name
 *             properties:
 *               name:
 *                 type: string
 *                 description: Brand name
 *                 maxLength: 50
 *               description:
 *                 type: string
 *                 description: Brand description
 *                 maxLength: 500
 *               logo:
 *                 type: string
 *                 description: URL to brand logo
 *               website:
 *                 type: string
 *                 description: Brand website URL
 *               category:
 *                 type: string
 *                 description: Brand category
 *                 enum: [Technology, Fashion, Automotive, Gaming, Beauty, Home & Kitchen, Sports, Health, Other]
 *               isActive:
 *                 type: boolean
 *                 description: Whether the brand is active
 *     responses:
 *       201:
 *         description: Brand created successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   $ref: '#/components/schemas/Brand'
 */
router.post(
  '/',
  [
    body('name', 'Brand name is required').not().isEmpty().trim(),
    body('name', 'Brand name cannot be more than 50 characters').isLength({ max: 50 }),
    body('description', 'Description cannot be more than 500 characters').optional().isLength({ max: 500 }),
    body('logo', 'Logo must be a valid URL').optional().isURL(),
    body('website', 'Website must be a valid URL').optional().isURL(),
    body('category', 'Invalid category').optional().isIn([
      'Technology', 'Fashion', 'Automotive', 'Gaming', 'Beauty', 
      'Home & Kitchen', 'Sports', 'Health', 'Other'
    ]),
    body('isActive', 'isActive must be a boolean').optional().isBoolean()
  ],
  createBrand
);

/**
 * @swagger
 * /admin/brands/{id}:
 *   get:
 *     summary: Get brand by ID (admin only)
 *     description: Retrieve a single brand by its ID
 *     tags: [Admin - Brands]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Brand ID
 *     responses:
 *       200:
 *         description: Brand retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   $ref: '#/components/schemas/Brand'
 */
router.get('/:id', getBrandById);

/**
 * @swagger
 * /admin/brands/{id}:
 *   put:
 *     summary: Update a brand (admin only)
 *     description: Update an existing brand
 *     tags: [Admin - Brands]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Brand ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               name:
 *                 type: string
 *                 description: Brand name
 *                 maxLength: 50
 *               description:
 *                 type: string
 *                 description: Brand description
 *                 maxLength: 500
 *               logo:
 *                 type: string
 *                 description: URL to brand logo
 *               website:
 *                 type: string
 *                 description: Brand website URL
 *               category:
 *                 type: string
 *                 description: Brand category
 *                 enum: [Technology, Fashion, Automotive, Gaming, Beauty, Home & Kitchen, Sports, Health, Other]
 *               isActive:
 *                 type: boolean
 *                 description: Whether the brand is active
 *     responses:
 *       200:
 *         description: Brand updated successfully
 */
router.put(
  '/:id',
  [
    body('name', 'Brand name cannot be empty').optional().not().isEmpty().trim(),
    body('name', 'Brand name cannot be more than 50 characters').optional().isLength({ max: 50 }),
    body('description', 'Description cannot be more than 500 characters').optional().isLength({ max: 500 }),
    body('logo', 'Logo must be a valid URL').optional().isURL(),
    body('website', 'Website must be a valid URL').optional().isURL(),
    body('category', 'Invalid category').optional().isIn([
      'Technology', 'Fashion', 'Automotive', 'Gaming', 'Beauty', 
      'Home & Kitchen', 'Sports', 'Health', 'Other'
    ]),
    body('isActive', 'isActive must be a boolean').optional().isBoolean()
  ],
  updateBrand
);

/**
 * @swagger
 * /admin/brands/{id}:
 *   delete:
 *     summary: Delete a brand (admin only)
 *     description: Delete a brand (only if no products are using it)
 *     tags: [Admin - Brands]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Brand ID
 *     responses:
 *       200:
 *         description: Brand deleted successfully
 */
router.delete('/:id', deleteBrand);

/**
 * @swagger
 * /admin/brands/{id}/toggle:
 *   patch:
 *     summary: Toggle brand active status (admin only)
 *     description: Toggle the active status of a brand
 *     tags: [Admin - Brands]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Brand ID
 *     responses:
 *       200:
 *         description: Brand status toggled successfully
 */
router.patch('/:id/toggle', toggleBrandStatus);

module.exports = router;
