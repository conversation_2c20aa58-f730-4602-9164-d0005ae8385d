/**
 * <PERSON><PERSON>t to test creating a product with the new category structure
 */

const mongoose = require('mongoose');
const Product = require('../models/Product');

// Load environment variables
require('dotenv').config({ path: './.env' });

const testProduct = async () => {
  try {
    // Connect to MongoDB
    console.log('Connecting to:', process.env.MONGODB_URI);
    await mongoose.connect(process.env.MONGODB_URI);
    console.log('Connected to MongoDB');

    // Test creating a product with valid category and subcategory
    console.log('\n=== Testing valid product creation ===');
    const validProduct = new Product({
      name: 'Test Blender',
      category: 'Home & Kitchen Gadgets',
      subcategory: 'Small Kitchen Appliances (blenders, air fryers)',
      brand: 'TestBrand',
      image: 'https://example.com/image.jpg',
      description: 'A test blender for testing purposes',
      pros: ['Fast blending', 'Easy to clean'],
      cons: ['Loud', 'Expensive'],
      sources: ['https://example.com/review1', 'Consumer Reports'],
      score: 85,
      affiliateLink: 'https://example.com/affiliate'
    });

    try {
      await validProduct.save();
      console.log('✓ Valid product created successfully');
      console.log(`Product ID: ${validProduct._id}`);
    } catch (error) {
      console.log('✗ Failed to create valid product:', error.message);
    }

    // Test creating a product with invalid category/subcategory combination
    console.log('\n=== Testing invalid product creation ===');
    const invalidProduct = new Product({
      name: 'Test Lipstick',
      category: 'Beauty & Skincare',
      subcategory: 'Small Kitchen Appliances (blenders, air fryers)', // Wrong subcategory for this category
      brand: 'TestBrand',
      image: 'https://example.com/image.jpg',
      description: 'A test lipstick for testing purposes',
      pros: ['Long lasting', 'Good color'],
      cons: ['Expensive'],
      sources: ['https://example.com/beauty-review', 'Allure Magazine'],
      score: 90,
      affiliateLink: 'https://example.com/affiliate'
    });

    try {
      await invalidProduct.save();
      console.log('✗ Invalid product was created (this should not happen)');
    } catch (error) {
      console.log('✓ Invalid product correctly rejected:', error.message);
    }

    // Test updating a product
    if (validProduct._id) {
      console.log('\n=== Testing product update ===');
      try {
        validProduct.subcategory = 'Smart Home Devices';
        await validProduct.save();
        console.log('✓ Product updated successfully');
      } catch (error) {
        console.log('✓ Product update correctly rejected:', error.message);
      }
    }

    // Clean up - delete the test product
    if (validProduct._id) {
      await Product.findByIdAndDelete(validProduct._id);
      console.log('\n✓ Test product cleaned up');
    }

  } catch (error) {
    console.error('Error:', error);
  } finally {
    await mongoose.disconnect();
    console.log('\nDisconnected from MongoDB');
  }
};

// Run the test
testProduct();
