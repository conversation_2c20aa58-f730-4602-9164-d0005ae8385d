const mongoose = require('mongoose');
require('dotenv').config({ path: '../config.env' });

// Connect to MongoDB
const connectDB = async () => {
  try {
    const conn = await mongoose.connect(process.env.MONGODB_URI);
    console.log(`MongoDB Connected: ${conn.connection.host}`);
  } catch (error) {
    console.error('Error connecting to MongoDB:', error);
    process.exit(1);
  }
};

// Migration script to convert opinion field to sources array
const migrateOpinionToSources = async () => {
  try {
    await connectDB();

    console.log('Starting migration: Converting opinion field to sources array...');

    // Get the products collection directly
    const db = mongoose.connection.db;
    const productsCollection = db.collection('products');

    // Find all products that have an opinion field but no sources field
    const productsToMigrate = await productsCollection.find({
      opinion: { $exists: true },
      sources: { $exists: false }
    }).toArray();

    console.log(`Found ${productsToMigrate.length} products to migrate`);

    if (productsToMigrate.length === 0) {
      console.log('No products need migration');
      return;
    }

    // Process each product
    let migratedCount = 0;
    let errorCount = 0;

    for (const product of productsToMigrate) {
      try {
        // Convert opinion to sources array
        // For now, we'll create a generic source entry since we don't have actual source URLs
        const sources = [
          'Editorial Review', // Generic source for existing opinions
          'Product Analysis'  // Another generic source
        ];

        // Update the product: add sources array and remove opinion field
        const result = await productsCollection.updateOne(
          { _id: product._id },
          {
            $set: { sources: sources },
            $unset: { opinion: "" }
          }
        );

        if (result.modifiedCount > 0) {
          migratedCount++;
          console.log(`✓ Migrated product: ${product.name} (${product._id})`);
        } else {
          console.log(`⚠ No changes made to product: ${product.name} (${product._id})`);
        }

      } catch (error) {
        errorCount++;
        console.error(`✗ Error migrating product ${product.name} (${product._id}):`, error.message);
      }
    }

    console.log('\n=== Migration Summary ===');
    console.log(`Total products found: ${productsToMigrate.length}`);
    console.log(`Successfully migrated: ${migratedCount}`);
    console.log(`Errors: ${errorCount}`);

    // Verify the migration
    console.log('\n=== Verification ===');
    const remainingOpinions = await productsCollection.countDocuments({ opinion: { $exists: true } });
    const newSources = await productsCollection.countDocuments({ sources: { $exists: true } });
    
    console.log(`Products with opinion field remaining: ${remainingOpinions}`);
    console.log(`Products with sources field: ${newSources}`);

    if (remainingOpinions === 0) {
      console.log('✓ Migration completed successfully! All opinion fields have been converted to sources.');
    } else {
      console.log('⚠ Some products still have opinion fields. You may need to run the migration again.');
    }

  } catch (error) {
    console.error('Migration failed:', error);
  } finally {
    await mongoose.disconnect();
    console.log('\nDisconnected from MongoDB');
  }
};

// Run the migration
console.log('=== Opinion to Sources Migration Script ===');
console.log('This script will convert existing opinion fields to sources arrays');
console.log('Starting migration...\n');

migrateOpinionToSources()
  .then(() => {
    console.log('\nMigration script completed');
    process.exit(0);
  })
  .catch((error) => {
    console.error('Migration script failed:', error);
    process.exit(1);
  });
