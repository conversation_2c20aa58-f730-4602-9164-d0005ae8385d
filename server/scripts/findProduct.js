/**
 * <PERSON><PERSON>t to find a specific product by ID
 */

const mongoose = require('mongoose');
const Product = require('../models/Product');

// Load environment variables
require('dotenv').config({ path: './.env' });

const findProduct = async () => {
  try {
    // Connect to MongoDB
    console.log('Connecting to:', process.env.MONGODB_URI);
    await mongoose.connect(process.env.MONGODB_URI);
    console.log('Connected to MongoDB');
    console.log('Database name:', mongoose.connection.db.databaseName);

    // Try to find the specific product from the error
    const productId = '6829d6992c533702a3af6ab9';
    console.log(`\nLooking for product with ID: ${productId}`);
    
    const product = await Product.findById(productId);
    if (product) {
      console.log('Found product:');
      console.log(`Name: ${product.name}`);
      console.log(`Category: "${product.category}"`);
      console.log(`Subcategory: "${product.subcategory}"`);
      console.log(`Brand: ${product.brand}`);
      console.log(`Created: ${product.createdAt}`);
    } else {
      console.log('Product not found');
    }

    // Also try to find all products in all collections
    console.log('\nChecking all collections in database...');
    const collections = await mongoose.connection.db.listCollections().toArray();
    console.log('Collections:', collections.map(c => c.name));

    // Check products collection specifically
    const db = mongoose.connection.db;
    const productsCollection = db.collection('products');
    const productCount = await productsCollection.countDocuments();
    console.log(`Products collection document count: ${productCount}`);

    if (productCount > 0) {
      const sampleProducts = await productsCollection.find({}).limit(3).toArray();
      console.log('\nSample products from collection:');
      sampleProducts.forEach((product, index) => {
        console.log(`${index + 1}. ${product.name || 'No name'}`);
        console.log(`   Category: "${product.category || 'No category'}"`);
        console.log(`   Subcategory: "${product.subcategory || 'No subcategory'}"`);
        console.log(`   ID: ${product._id}`);
      });
    }

  } catch (error) {
    console.error('Error:', error);
  } finally {
    await mongoose.disconnect();
    console.log('\nDisconnected from MongoDB');
  }
};

// Run the check
findProduct();
