/**
 * <PERSON><PERSON>t to check existing products and their categories
 */

const mongoose = require('mongoose');
const Product = require('../models/Product');
const { PRODUCT_CATEGORIES, PRODUCT_SUBCATEGORIES } = require('../utils/constants');

// Load environment variables
require('dotenv').config({ path: './.env' });

const checkProducts = async () => {
  try {
    // Connect to MongoDB
    console.log('Connecting to:', process.env.MONGODB_URI);
    await mongoose.connect(process.env.MONGODB_URI);
    console.log('Connected to MongoDB');
    console.log('Database name:', mongoose.connection.db.databaseName);

    // Get all products
    const products = await Product.find({});
    console.log(`Found ${products.length} products`);

    // Show first few products for debugging
    if (products.length > 0) {
      console.log('\nFirst few products:');
      products.slice(0, 3).forEach((product, index) => {
        console.log(`${index + 1}. ${product.name}`);
        console.log(`   Category: "${product.category}"`);
        console.log(`   Subcategory: "${product.subcategory}"`);
        console.log(`   ID: ${product._id}`);
      });
    }

    // Check for products with invalid categories
    const invalidCategoryProducts = products.filter(product =>
      !PRODUCT_CATEGORIES.includes(product.category)
    );

    console.log('\n=== CATEGORY ANALYSIS ===');
    console.log('Valid categories:', PRODUCT_CATEGORIES);
    console.log(`Products with invalid categories: ${invalidCategoryProducts.length}`);

    if (invalidCategoryProducts.length > 0) {
      console.log('\nProducts with invalid categories:');
      invalidCategoryProducts.forEach(product => {
        console.log(`- ${product.name}: "${product.category}"`);
      });
    }

    // Check for products missing subcategory
    const missingSubcategoryProducts = products.filter(product =>
      !product.subcategory
    );

    console.log(`\nProducts missing subcategory: ${missingSubcategoryProducts.length}`);
    if (missingSubcategoryProducts.length > 0) {
      console.log('Products missing subcategory:');
      missingSubcategoryProducts.forEach(product => {
        console.log(`- ${product.name}: category="${product.category}"`);
      });
    }

    // Check for products with invalid subcategories
    const invalidSubcategoryProducts = products.filter(product => {
      if (!product.subcategory || !product.category) return false;
      const validSubcategories = PRODUCT_SUBCATEGORIES[product.category] || [];
      return !validSubcategories.includes(product.subcategory);
    });

    console.log(`\nProducts with invalid subcategories: ${invalidSubcategoryProducts.length}`);
    if (invalidSubcategoryProducts.length > 0) {
      console.log('Products with invalid subcategories:');
      invalidSubcategoryProducts.forEach(product => {
        const validSubcategories = PRODUCT_SUBCATEGORIES[product.category] || [];
        console.log(`- ${product.name}: category="${product.category}", subcategory="${product.subcategory}"`);
        console.log(`  Valid subcategories for ${product.category}:`, validSubcategories);
      });
    }

    // Show unique categories found in database
    const uniqueCategories = [...new Set(products.map(p => p.category))];
    console.log('\n=== UNIQUE CATEGORIES IN DATABASE ===');
    uniqueCategories.forEach(category => {
      const count = products.filter(p => p.category === category).length;
      const isValid = PRODUCT_CATEGORIES.includes(category);
      console.log(`- "${category}" (${count} products) ${isValid ? '✓' : '✗'}`);
    });

  } catch (error) {
    console.error('Error checking products:', error);
  } finally {
    await mongoose.disconnect();
    console.log('\nDisconnected from MongoDB');
  }
};

// Run the check
checkProducts();
