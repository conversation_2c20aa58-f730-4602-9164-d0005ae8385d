# MongoDB Indexes for Halocritique Opinions

This document explains the MongoDB indexes used in the Halocritique Opinions application to improve performance, especially for text search.

## Running the Index Creation Script

To create all the necessary indexes, run:

```bash
npm run create-indexes
```

This will execute the `createIndexes.js` script which sets up all the required indexes for optimal performance.

## Indexes Created

### Product Collection

1. **Text Index with Weights**
   - Fields: `name`, `brand`, `category`, `description`
   - Weights: name (10), brand (5), category (3), description (1)
   - Purpose: Optimized text search with prioritization of fields

2. **Name Index**
   - Field: `name`
   - Purpose: Support for autocomplete and prefix matching

3. **Brand Index**
   - Field: `brand`
   - Purpose: Filtering by brand

4. **Category Index**
   - Field: `category`
   - Purpose: Filtering by category

5. **Score Index**
   - Field: `score`
   - Purpose: Sorting by score

6. **Category + Brand Compound Index**
   - Fields: `category`, `brand`
   - Purpose: Efficient filtering by both category and brand

7. **Category + Score Compound Index**
   - Fields: `category`, `score`
   - Purpose: Efficient filtering by category with sorting by score

8. **CreatedAt Index**
   - Field: `createdAt`
   - Purpose: Sorting by newest

9. **UpdatedAt Index**
   - Field: `updatedAt`
   - Purpose: Sorting by recently updated

10. **Slug Index**
    - Field: `slug`
    - Purpose: Fast lookup by slug

### User Collection

1. **Email Index**
   - Field: `email`
   - Purpose: Fast lookup for login and ensuring uniqueness

2. **Role Index**
   - Field: `role`
   - Purpose: Filtering users by role (admin vs regular users)

3. **CreatedAt Index**
   - Field: `createdAt`
   - Purpose: Sorting users by creation date

## Performance Benefits

These indexes provide several performance benefits:

1. **Faster Text Search**: The weighted text index improves search relevance and speed.
2. **Efficient Filtering**: Dedicated indexes for common filter fields like category and brand.
3. **Optimized Sorting**: Indexes for common sort fields like score, createdAt, and updatedAt.
4. **Better Autocomplete**: The name index with prefix matching improves autocomplete suggestions.
5. **Compound Indexes**: Support for common query patterns that filter and sort simultaneously.

## Monitoring Index Usage

You can monitor index usage in MongoDB Compass by:

1. Connect to your database
2. Go to the "Performance" tab
3. Check "Index Usage" to see which indexes are being used and their effectiveness

## Index Maintenance

Indexes should be reviewed periodically as your application evolves:

1. **Add new indexes** as new query patterns emerge
2. **Remove unused indexes** that may be slowing down writes
3. **Update text index weights** if search relevance needs adjustment

Remember that while indexes speed up reads, they slow down writes. Only create indexes that support your actual query patterns.
