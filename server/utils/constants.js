/**
 * Constants used throughout the application
 */

// Product categories - 5 core launch categories optimized for high volume, strong commissions, and Western market buying behaviour
exports.PRODUCT_CATEGORIES = [
  'Beauty & Skincare',
  'Home & Kitchen Gadgets',
  'Electronics',
  'Fashion & Apparel',
  'Watches, Jewelry & Footwear'
];

// Product subcategories mapped to their parent categories
exports.PRODUCT_SUBCATEGORIES = {
  'Beauty & Skincare': [
    'Facial Cleansers',
    'Moisturizers & Serums',
    'Anti-Aging Products',
    'Makeup Tools & Brushes',
    'Foundation & Concealers',
    'Lipsticks & Lip Glosses',
    'Eye Makeup (Mascara, Liner, Shadow)',
    'Sunscreens & SPF Products',
    'Haircare (Shampoo, Conditioner, Treatments)',
    'Fragrances & Perfumes'
  ],
  'Home & Kitchen Gadgets': [
    'Small Kitchen Appliances (blenders, air fryers)',
    'Smart Home Devices',
    'Storage & Organization',
    'Cookware Sets',
    'Cleaning Tools & Tech (vacuums, steam mops)',
    'Coffee & Espresso Machines',
    'Food Prep Tools (slicers, peelers)',
    'Home Safety & Security (alarms, sensors)',
    'Eco-Friendly Gadgets',
    'Lighting (smart bulbs, LED strips)'
  ],
  'Electronics': [
    'Headphones & Earbuds',
    'Smartwatches & Wearables',
    'Tablets & e-Readers',
    'Streaming Devices',
    'Wireless Chargers',
    'Bluetooth Speakers',
    'Laptop Accessories',
    'Gaming Accessories (controllers, headsets)',
    'Smart TVs',
    'Action & Security Cameras'
  ],
  'Fashion & Apparel': [
    'Men\'s Clothing',
    'Women\'s Clothing',
    'Outerwear (jackets, coats)',
    'Sportswear & Athleisure',
    'Accessories (hats, scarves, belts)',
    'Jeans & Trousers',
    'Dresses & Jumpsuits',
    'Plus Size Clothing',
    'Sustainable Fashion',
    'Underwear & Loungewear'
  ],
  'Watches, Jewelry & Footwear': [
    'Men\'s Watches',
    'Women\'s Watches',
    'Smartwatches',
    'Fine Jewelry (Gold, Diamond, etc.)',
    'Fashion Jewelry',
    'Men\'s Shoes (sneakers, dress, boots)',
    'Women\'s Shoes (heels, flats, sandals)',
    'Footwear Accessories',
    'Luxury Jewelry',
    'Luxury Watches'
  ]
};

// Get all subcategories as a flat array for validation
exports.ALL_SUBCATEGORIES = Object.values(exports.PRODUCT_SUBCATEGORIES).flat();

// Product score is now a percentage (0-100) instead of predefined categories
