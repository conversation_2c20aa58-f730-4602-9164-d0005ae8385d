const Product = require('../models/Product');

/**
 * Calculate relevance score for search results
 * @param {Object} product - Product document
 * @param {Array} searchTerms - Array of search terms
 * @returns {Number} Relevance score
 */
const calculateRelevanceScore = (product, searchTerms) => {
  let score = 0;
  const name = (product.name || '').toLowerCase();
  const brand = (product.brand || '').toLowerCase();
  const category = (product.category || '').toLowerCase();
  const subcategory = (product.subcategory || '').toLowerCase();
  const keywords = (product.keywords || []).map(k => k.toLowerCase());

  searchTerms.forEach(term => {
    const termLower = term.toLowerCase();

    // Exact match in name (highest priority)
    if (name === termLower) score += 100;
    else if (name.startsWith(termLower)) score += 80;
    else if (name.includes(termLower)) score += 40;

    // Exact match in keywords (high priority)
    if (keywords.includes(termLower)) score += 90;
    else if (keywords.some(k => k.startsWith(termLower))) score += 70;
    else if (keywords.some(k => k.includes(termLower))) score += 35;

    // Brand matches (medium priority)
    if (brand === termLower) score += 60;
    else if (brand.startsWith(termLower)) score += 50;
    else if (brand.includes(termLower)) score += 25;

    // Category matches (lower priority)
    if (category === termLower) score += 30;
    else if (category.startsWith(termLower)) score += 25;
    else if (category.includes(termLower)) score += 15;

    // Subcategory matches (lowest priority)
    if (subcategory === termLower) score += 20;
    else if (subcategory.startsWith(termLower)) score += 15;
    else if (subcategory.includes(termLower)) score += 10;
  });

  return score;
};

/**
 * @desc    Search products by query
 * @route   GET /api/search
 * @access  Public
 */
exports.searchProducts = async (req, res) => {
  try {
    const { q, category, subcategory, brand, minScore, maxScore, sort = 'relevance', page = 1, limit = 16 } = req.query;

    // Build query object
    const query = {};
    let useTextSearch = false;
    let searchTerms = [];

    // Improved search logic with better relevance
    if (q && q.trim() !== '') {
      const searchQuery = q.trim();
      searchTerms = searchQuery.toLowerCase().split(/\s+/).filter(term => term.length > 0);

      // Use MongoDB text search for better relevance when query is substantial
      if (searchQuery.length >= 3) {
        try {
          // Try text search first for better relevance
          query.$text = { $search: searchQuery };
          useTextSearch = true;
        } catch (error) {
          console.log('Text search failed, falling back to regex');
          useTextSearch = false;
        }
      }

      // Fallback to improved regex search with relevance scoring
      if (!useTextSearch) {
        const escapedQuery = searchQuery.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');

        // Create multiple search patterns for better matching
        const exactMatch = new RegExp(`\\b${escapedQuery}\\b`, 'i');
        const startsWith = new RegExp(`^${escapedQuery}`, 'i');
        const contains = new RegExp(escapedQuery, 'i');

        // Build search conditions with priority
        const searchConditions = [];

        // Highest priority: exact matches in name and keywords
        searchConditions.push(
          { name: exactMatch },
          { keywords: { $in: [exactMatch] } }
        );

        // Medium priority: starts with matches
        searchConditions.push(
          { name: startsWith },
          { brand: startsWith },
          { category: startsWith },
          { subcategory: startsWith }
        );

        // Lower priority: contains matches
        searchConditions.push(
          { name: contains },
          { keywords: contains },
          { brand: contains },
          { category: contains },
          { subcategory: contains },
          { description: contains }
        );

        query.$or = searchConditions;
      }
    }

    // Add filters - these should work with both search and non-search queries
    const filters = {};

    if (category && category.trim()) {
      filters.category = { $regex: new RegExp(`^${category.trim()}$`, 'i') };
    }

    if (subcategory && subcategory.trim()) {
      filters.subcategory = { $regex: new RegExp(`^${subcategory.trim()}$`, 'i') };
    }

    if (brand && brand.trim()) {
      filters.brand = { $regex: new RegExp(`^${brand.trim()}$`, 'i') };
    }

    // Add score range if provided
    if (minScore || maxScore) {
      filters.score = {};
      if (minScore) filters.score.$gte = parseInt(minScore);
      if (maxScore) filters.score.$lte = parseInt(maxScore);
    }

    // Combine search query with filters
    if (Object.keys(filters).length > 0) {
      if (Object.keys(query).length > 0) {
        // If we have both search and filters, combine them with $and
        query.$and = [query, filters];
      } else {
        // If we only have filters, use them directly
        Object.assign(query, filters);
      }
    }

    // If no search criteria provided, return empty array
    if (Object.keys(query).length === 0) {
      return res.status(200).json({
        success: true,
        data: [],
        pagination: {
          page: 1,
          limit: parseInt(limit),
          totalPages: 0,
          total: 0,
          hasNextPage: false,
          hasPrevPage: false
        }
      });
    }

    // Calculate pagination
    const pageNum = parseInt(page, 10);
    const limitNum = parseInt(limit, 10);
    const skip = (pageNum - 1) * limitNum;

    // Determine sort order with improved relevance
    let sortOptions = {};

    if (useTextSearch && (sort === 'relevance' || !sort)) {
      // For text search, sort by text score (relevance) first, then by product score
      sortOptions = { textScore: { $meta: 'textScore' }, score: -1 };
    } else if (sort === 'relevance' && searchTerms.length > 0) {
      // For regex search, we'll do post-processing relevance sorting
      sortOptions = { score: -1, name: 1 };
    } else if (sort === 'score') {
      sortOptions = { score: -1, name: 1 };
    } else if (sort === 'brand') {
      sortOptions = { brand: 1, score: -1 };
    } else if (sort === 'newest') {
      sortOptions = { createdAt: -1 };
    } else if (sort === 'updated') {
      sortOptions = { updatedAt: -1 };
    } else if (sort === 'name') {
      sortOptions = { name: 1 };
    } else {
      // Default to relevance-based sorting for search queries
      if (q && q.trim()) {
        sortOptions = { score: -1, name: 1 };
      } else {
        sortOptions = { brand: 1, score: -1 };
      }
    }

    // Execute query with pagination and sorting
    let products;

    if (useTextSearch && (sort === 'relevance' || !sort)) {
      // For text search with relevance sorting
      products = await Product.find(query, { textScore: { $meta: 'textScore' } })
        .select('name slug brand category subcategory image score')
        .sort({ textScore: { $meta: 'textScore' }, score: -1 })
        .skip(skip)
        .limit(limitNum);
    } else {
      products = await Product.find(query)
        .select('name slug brand category subcategory image score')
        .sort(sortOptions)
        .skip(skip)
        .limit(limitNum);
    }

    // Post-process for relevance if using regex search
    if (!useTextSearch && searchTerms.length > 0 && (sort === 'relevance' || !sort)) {
      products = products.sort((a, b) => {
        const aRelevance = calculateRelevanceScore(a, searchTerms);
        const bRelevance = calculateRelevanceScore(b, searchTerms);

        if (aRelevance !== bRelevance) {
          return bRelevance - aRelevance; // Higher relevance first
        }

        // If relevance is equal, sort by product score
        return b.score - a.score;
      });
    }

    // Get total count for pagination
    const total = await Product.countDocuments(query);

    // Calculate pagination info
    const totalPages = Math.ceil(total / limitNum);
    const hasNextPage = pageNum < totalPages;
    const hasPrevPage = pageNum > 1;

    // No fallback needed since we're using regex as primary search

    res.status(200).json({
      success: true,
      count: products.length,
      pagination: {
        page: pageNum,
        limit: limitNum,
        totalPages,
        total,
        hasNextPage,
        hasPrevPage
      },
      data: products
    });
  } catch (err) {
    console.error('Search error:', err);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};

/**
 * @desc    Get search suggestions
 * @route   GET /api/search/suggestions
 * @access  Public
 */
exports.getSearchSuggestions = async (req, res) => {
  try {
    const { q } = req.query;

    if (!q || q.trim() === '') {
      return res.status(200).json({
        success: true,
        data: []
      });
    }

    // For product name suggestions, use prefix matching for better autocomplete
    const nameRegex = new RegExp(`^${q}`, 'i');

    // For brand and category, we can use the text index if the query is long enough
    // Otherwise use regex for shorter queries (better for autocomplete)
    const useTextIndex = q.length >= 3;

    // Get distinct product names, brands, categories, and subcategories that match the query
    const [products, brands, categories, subcategories] = await Promise.all([
      // For product names, prioritize exact matches then prefix matches
      Product.find({
        $or: [
          { name: { $regex: nameRegex } },
          { keywords: { $regex: nameRegex } }
        ]
      })
        .select('name slug')
        .limit(8)
        .sort({ name: 1 }),

      // For brands, prioritize prefix matches for better autocomplete
      Product.distinct('brand', {
        brand: { $regex: new RegExp(`^${q}`, 'i') }
      }).then(exactBrands => {
        if (exactBrands.length < 3) {
          // If not enough exact matches, get partial matches
          return Product.distinct('brand', {
            brand: { $regex: new RegExp(q, 'i') }
          });
        }
        return exactBrands;
      }),

      // For categories, use prefix matching
      Product.distinct('category', {
        category: { $regex: new RegExp(`^${q}`, 'i') }
      }),

      // For subcategories, use text index for longer queries, regex for shorter ones
      useTextIndex
        ? Product.distinct('subcategory', { $text: { $search: q } })
        : Product.distinct('subcategory', { subcategory: { $regex: new RegExp(q, 'i') } })
    ]);

    // Format suggestions
    const productSuggestions = products.map(p => ({
      type: 'product',
      text: p.name,
      slug: p.slug
    }));

    const brandSuggestions = brands.slice(0, 5).map(brand => ({
      type: 'brand',
      text: brand
    }));

    const categorySuggestions = categories.slice(0, 3).map(category => ({
      type: 'category',
      text: category
    }));

    const subcategorySuggestions = subcategories.slice(0, 3).map(subcategory => ({
      type: 'subcategory',
      text: subcategory
    }));

    // Combine all suggestions
    const suggestions = [
      ...productSuggestions,
      ...brandSuggestions,
      ...categorySuggestions,
      ...subcategorySuggestions
    ].slice(0, 10);

    res.status(200).json({
      success: true,
      data: suggestions
    });
  } catch (err) {
    console.error('Search suggestions error:', err);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};
