const Brand = require('../models/Brand');
const Product = require('../models/Product');

/**
 * @desc    Get all brands for admin
 * @route   GET /api/admin/brands
 * @access  Private (Admin)
 */
exports.getAdminBrands = async (req, res) => {
  try {
    const { page = 1, limit = 50, search, category, isActive } = req.query;

    // Build query
    const query = {};
    
    if (search) {
      query.$text = { $search: search };
    }
    
    if (category && category !== 'all') {
      query.category = category;
    }
    
    if (isActive !== undefined) {
      query.isActive = isActive === 'true';
    }

    // Pagination
    const pageNum = parseInt(page, 10);
    const limitNum = parseInt(limit, 10);
    const startIndex = (pageNum - 1) * limitNum;

    // Get total count
    const total = await Brand.countDocuments(query);
    const totalPages = Math.ceil(total / limitNum);

    // Execute query with pagination
    const brands = await Brand.find(query)
      .sort({ createdAt: -1 })
      .skip(startIndex)
      .limit(limitNum);

    // Get product counts for each brand
    const brandsWithCounts = await Promise.all(
      brands.map(async (brand) => {
        const productCount = await Product.countDocuments({ 
          brand: { $regex: new RegExp(`^${brand.name}$`, 'i') }
        });
        return {
          ...brand.toObject(),
          productCount
        };
      })
    );

    // Pagination info
    const pagination = {
      page: pageNum,
      limit: limitNum,
      totalPages,
      total,
      hasNextPage: pageNum < totalPages,
      hasPrevPage: pageNum > 1
    };

    if (pagination.hasNextPage) {
      pagination.next = { page: pageNum + 1, limit: limitNum };
    }

    if (pagination.hasPrevPage) {
      pagination.prev = { page: pageNum - 1, limit: limitNum };
    }

    res.status(200).json({
      success: true,
      count: brandsWithCounts.length,
      pagination,
      data: brandsWithCounts
    });
  } catch (err) {
    console.error('Error fetching admin brands:', err);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};

/**
 * @desc    Create new brand
 * @route   POST /api/admin/brands
 * @access  Private (Admin)
 */
exports.createBrand = async (req, res) => {
  try {
    const brand = await Brand.create(req.body);

    res.status(201).json({
      success: true,
      data: brand
    });
  } catch (err) {
    console.error('Error creating brand:', err);

    if (err.code === 11000) {
      return res.status(400).json({
        success: false,
        message: 'A brand with this name already exists'
      });
    }

    if (err.name === 'ValidationError') {
      const errors = Object.values(err.errors).map(val => val.message);
      return res.status(400).json({
        success: false,
        message: errors.join(', ')
      });
    }

    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};

/**
 * @desc    Update brand
 * @route   PUT /api/admin/brands/:id
 * @access  Private (Admin)
 */
exports.updateBrand = async (req, res) => {
  try {
    const brand = await Brand.findByIdAndUpdate(
      req.params.id,
      req.body,
      {
        new: true,
        runValidators: true
      }
    );

    if (!brand) {
      return res.status(404).json({
        success: false,
        message: 'Brand not found'
      });
    }

    res.status(200).json({
      success: true,
      data: brand
    });
  } catch (err) {
    console.error('Error updating brand:', err);

    if (err.code === 11000) {
      return res.status(400).json({
        success: false,
        message: 'A brand with this name already exists'
      });
    }

    if (err.name === 'ValidationError') {
      const errors = Object.values(err.errors).map(val => val.message);
      return res.status(400).json({
        success: false,
        message: errors.join(', ')
      });
    }

    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};

/**
 * @desc    Delete brand
 * @route   DELETE /api/admin/brands/:id
 * @access  Private (Admin)
 */
exports.deleteBrand = async (req, res) => {
  try {
    const brand = await Brand.findById(req.params.id);

    if (!brand) {
      return res.status(404).json({
        success: false,
        message: 'Brand not found'
      });
    }

    // Check if brand has products
    const productCount = await Product.countDocuments({ 
      brand: { $regex: new RegExp(`^${brand.name}$`, 'i') }
    });

    if (productCount > 0) {
      return res.status(400).json({
        success: false,
        message: `Cannot delete brand. ${productCount} products are using this brand.`
      });
    }

    await Brand.findByIdAndDelete(req.params.id);

    res.status(200).json({
      success: true,
      data: {}
    });
  } catch (err) {
    console.error('Error deleting brand:', err);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};

/**
 * @desc    Get brand by ID
 * @route   GET /api/admin/brands/:id
 * @access  Private (Admin)
 */
exports.getBrandById = async (req, res) => {
  try {
    const brand = await Brand.findById(req.params.id);

    if (!brand) {
      return res.status(404).json({
        success: false,
        message: 'Brand not found'
      });
    }

    // Get product count
    const productCount = await Product.countDocuments({ 
      brand: { $regex: new RegExp(`^${brand.name}$`, 'i') }
    });

    res.status(200).json({
      success: true,
      data: {
        ...brand.toObject(),
        productCount
      }
    });
  } catch (err) {
    console.error('Error fetching brand:', err);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};

/**
 * @desc    Toggle brand active status
 * @route   PATCH /api/admin/brands/:id/toggle
 * @access  Private (Admin)
 */
exports.toggleBrandStatus = async (req, res) => {
  try {
    const brand = await Brand.findById(req.params.id);

    if (!brand) {
      return res.status(404).json({
        success: false,
        message: 'Brand not found'
      });
    }

    brand.isActive = !brand.isActive;
    await brand.save();

    res.status(200).json({
      success: true,
      data: brand
    });
  } catch (err) {
    console.error('Error toggling brand status:', err);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};
