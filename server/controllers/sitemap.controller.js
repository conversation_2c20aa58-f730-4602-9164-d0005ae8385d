const Product = require('../models/Product');
const Brand = require('../models/Brand');

/**
 * Generate XML sitemap
 */
exports.generateSitemap = async (req, res) => {
  try {
    const baseUrl = process.env.FRONTEND_URL || 'https://halocritique.com';
    
    // Static pages
    const staticPages = [
      { url: '/', priority: '1.0', changefreq: 'daily' },
      { url: '/about', priority: '0.8', changefreq: 'monthly' },
      { url: '/brands', priority: '0.9', changefreq: 'weekly' },
      { url: '/categories', priority: '0.9', changefreq: 'weekly' }
    ];

    // Get all products
    const products = await Product.find({ isActive: true })
      .select('slug updatedAt')
      .sort({ updatedAt: -1 })
      .limit(50000); // Limit for performance

    // Get all brands
    const brands = await Brand.find({ isActive: true })
      .select('slug updatedAt')
      .sort({ updatedAt: -1 });

    // Generate XML
    let xml = `<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">`;

    // Add static pages
    staticPages.forEach(page => {
      xml += `
  <url>
    <loc>${baseUrl}${page.url}</loc>
    <lastmod>${new Date().toISOString().split('T')[0]}</lastmod>
    <changefreq>${page.changefreq}</changefreq>
    <priority>${page.priority}</priority>
  </url>`;
    });

    // Add product pages
    products.forEach(product => {
      const lastmod = product.updatedAt ? product.updatedAt.toISOString().split('T')[0] : new Date().toISOString().split('T')[0];
      xml += `
  <url>
    <loc>${baseUrl}/products/${product.slug}</loc>
    <lastmod>${lastmod}</lastmod>
    <changefreq>weekly</changefreq>
    <priority>0.7</priority>
  </url>`;
    });

    // Add brand pages
    brands.forEach(brand => {
      const lastmod = brand.updatedAt ? brand.updatedAt.toISOString().split('T')[0] : new Date().toISOString().split('T')[0];
      xml += `
  <url>
    <loc>${baseUrl}/brands/${brand.slug}</loc>
    <lastmod>${lastmod}</lastmod>
    <changefreq>weekly</changefreq>
    <priority>0.6</priority>
  </url>`;
    });

    xml += `
</urlset>`;

    res.set({
      'Content-Type': 'application/xml',
      'Cache-Control': 'public, max-age=3600' // Cache for 1 hour
    });
    
    res.send(xml);
  } catch (error) {
    console.error('Error generating sitemap:', error);
    res.status(500).json({
      success: false,
      message: 'Error generating sitemap'
    });
  }
};

/**
 * Generate robots.txt
 */
exports.generateRobotsTxt = async (req, res) => {
  try {
    const baseUrl = process.env.FRONTEND_URL || 'https://halocritique.com';
    
    const robotsTxt = `User-agent: *
Allow: /
Disallow: /admin
Disallow: /api/
Disallow: /*?*sort=
Disallow: /*?*page=

# Sitemap
Sitemap: ${baseUrl}/sitemap.xml

# Crawl-delay for respectful crawling
Crawl-delay: 1`;

    res.set({
      'Content-Type': 'text/plain',
      'Cache-Control': 'public, max-age=86400' // Cache for 24 hours
    });
    
    res.send(robotsTxt);
  } catch (error) {
    console.error('Error generating robots.txt:', error);
    res.status(500).send('Error generating robots.txt');
  }
};

/**
 * Generate structured data for homepage
 */
exports.getStructuredData = async (req, res) => {
  try {
    const baseUrl = process.env.FRONTEND_URL || 'https://halocritique.com';
    
    // Get some featured products for structured data
    const featuredProducts = await Product.find({ isActive: true })
      .select('name slug brand category score image description')
      .sort({ score: -1 })
      .limit(10);

    const structuredData = {
      '@context': 'https://schema.org',
      '@type': 'WebSite',
      name: 'Halocritique',
      description: 'Find honest, unbiased product reviews and comparisons. Make informed purchasing decisions with expert-curated opinions and detailed analysis.',
      url: baseUrl,
      potentialAction: {
        '@type': 'SearchAction',
        target: `${baseUrl}/search?q={search_term_string}`,
        'query-input': 'required name=search_term_string'
      },
      publisher: {
        '@type': 'Organization',
        name: 'Halocritique',
        url: baseUrl,
        logo: {
          '@type': 'ImageObject',
          url: `${baseUrl}/logo.png`,
          width: 600,
          height: 60
        }
      },
      mainEntity: {
        '@type': 'ItemList',
        name: 'Featured Products',
        itemListElement: featuredProducts.map((product, index) => ({
          '@type': 'ListItem',
          position: index + 1,
          item: {
            '@type': 'Product',
            name: product.name,
            url: `${baseUrl}/products/${product.slug}`,
            brand: {
              '@type': 'Brand',
              name: product.brand
            },
            category: product.category,
            image: product.image,
            description: product.description,
            aggregateRating: {
              '@type': 'AggregateRating',
              ratingValue: product.score / 20, // Convert percentage to 5-star rating
              bestRating: 5,
              worstRating: 1,
              reviewCount: 1
            }
          }
        }))
      }
    };

    res.json(structuredData);
  } catch (error) {
    console.error('Error generating structured data:', error);
    res.status(500).json({
      success: false,
      message: 'Error generating structured data'
    });
  }
};
