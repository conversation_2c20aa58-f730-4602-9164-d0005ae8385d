const ProductClick = require('../models/ProductClick');
const Product = require('../models/Product');
const { validationResult } = require('express-validator');

/**
 * @desc    Track a product click
 * @route   POST /api/analytics/product-click
 * @access  Public
 */
exports.trackProductClick = async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        errors: errors.array()
      });
    }

    const { productId } = req.body;

    // Verify the product exists
    const product = await Product.findById(productId);
    if (!product) {
      return res.status(404).json({
        success: false,
        message: 'Product not found'
      });
    }

    // Create click record
    const clickData = {
      productId,
      // If user is authenticated, store userId
      userId: req.user ? req.user.id : null,
      // Store session ID if available
      sessionId: req.body.sessionId || null,
      // Store IP address (with privacy considerations)
      ipAddress: req.ip || req.connection.remoteAddress,
      // Store user agent
      userAgent: req.headers['user-agent'] || null,
      // Store referrer if available
      referrer: req.headers.referer || req.body.referrer || null
    };

    await ProductClick.create(clickData);

    res.status(200).json({
      success: true,
      message: 'Click tracked successfully'
    });
  } catch (err) {
    console.error('Error tracking product click:', err);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};

/**
 * @desc    Get click count for a product
 * @route   GET /api/analytics/product-clicks/:id
 * @access  Private (Admin)
 */
exports.getProductClickCount = async (req, res) => {
  try {
    const productId = req.params.id;

    // Verify the product exists
    const product = await Product.findById(productId);
    if (!product) {
      return res.status(404).json({
        success: false,
        message: 'Product not found'
      });
    }

    // Get click count
    const count = await ProductClick.countDocuments({ productId });

    res.status(200).json({
      success: true,
      data: {
        productId,
        productName: product.name,
        clickCount: count
      }
    });
  } catch (err) {
    console.error('Error getting product click count:', err);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};


