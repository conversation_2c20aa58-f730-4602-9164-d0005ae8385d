const { PRODUCT_CATEGORIES, PRODUCT_SUBCATEGORIES } = require('../utils/constants');

/**
 * @desc    Get all product categories
 * @route   GET /api/categories
 * @access  Public
 */
exports.getCategories = (req, res) => {
  res.status(200).json({
    success: true,
    data: PRODUCT_CATEGORIES
  });
};

/**
 * @desc    Get all product categories with their subcategories
 * @route   GET /api/categories/with-subcategories
 * @access  Public
 */
exports.getCategoriesWithSubcategories = (req, res) => {
  const categoriesWithSubcategories = PRODUCT_CATEGORIES.map(category => ({
    category,
    subcategories: PRODUCT_SUBCATEGORIES[category] || []
  }));

  res.status(200).json({
    success: true,
    data: categoriesWithSubcategories
  });
};

/**
 * @desc    Get subcategories for a specific category
 * @route   GET /api/categories/:category/subcategories
 * @access  Public
 */
exports.getSubcategories = (req, res) => {
  const { category } = req.params;

  if (!PRODUCT_CATEGORIES.includes(category)) {
    return res.status(404).json({
      success: false,
      message: `Category '${category}' not found. Available categories: ${PRODUCT_CATEGORIES.join(', ')}`
    });
  }

  const subcategories = PRODUCT_SUBCATEGORIES[category] || [];

  res.status(200).json({
    success: true,
    data: subcategories
  });
};

// Score is now a percentage (0-100) instead of predefined categories
