# Halo Review Developer Guide

This guide provides comprehensive documentation for developers who will maintain and extend the Halo Review application.

## Table of Contents

1. [Architecture Overview](#architecture-overview)
2. [Technology Stack](#technology-stack)
3. [Project Structure](#project-structure)
4. [Setup and Installation](#setup-and-installation)
5. [API Documentation](#api-documentation)
6. [Frontend Development](#frontend-development)
7. [Backend Development](#backend-development)
8. [Database](#database)
9. [Caching](#caching)
10. [Security](#security)
11. [Performance Optimization](#performance-optimization)
12. [Testing](#testing)
13. [Deployment](#deployment)
14. [Contribution Guidelines](#contribution-guidelines)

## Architecture Overview

Halocritique Review is a full-stack web application with a React frontend and Node.js/Express backend. It follows a client-server architecture with a RESTful API.

### High-Level Architecture

```
┌─────────────┐     ┌─────────────┐     ┌─────────────┐
│             │     │             │     │             │
│   Frontend  │────▶│   Backend   │────▶│   Database  │
│   (React)   │     │  (Express)  │     │  (MongoDB)  │
│             │◀────│             │◀────│             │
└─────────────┘     └─────────────┘     └─────────────┘
```

## Technology Stack

### Frontend
- React (with Vite)
- React Router for routing
- Tailwind CSS for styling
- Shadcn UI components
- TypeScript

### Backend
- Node.js
- Express.js
- MongoDB (with Mongoose)
- JWT for authentication
- Swagger for API documentation

### DevOps
- Git for version control
- npm for package management

## Project Structure

The project follows a monorepo structure with frontend and backend code in the same repository.

```
halo-review/
├── docs/                  # Documentation
├── public/                # Public assets
├── server/                # Backend code
│   ├── config/            # Configuration files
│   ├── controllers/       # Route controllers
│   ├── middleware/        # Express middleware
│   ├── models/            # Mongoose models
│   ├── routes/            # Express routes
│   ├── utils/             # Utility functions
│   └── server.js          # Entry point
├── src/                   # Frontend code
│   ├── components/        # React components
│   ├── hooks/             # Custom React hooks
│   ├── lib/               # Utility libraries
│   ├── pages/             # Page components
│   ├── services/          # API services
│   ├── utils/             # Utility functions
│   ├── App.tsx            # Main App component
│   └── main.tsx           # Entry point
├── .env                   # Environment variables
├── package.json           # Project dependencies
└── README.md              # Project overview
```

## Setup and Installation

### Prerequisites
- Node.js (v14 or higher)
- npm (v7 or higher)
- MongoDB (v4 or higher)

### Installation Steps

1. Clone the repository:
   ```bash
   git clone https://github.com/yourusername/halo-review.git
   cd halo-review
   ```

2. Install dependencies:
   ```bash
   npm install
   ```

3. Set up environment variables:
   - Create a `.env` file in the root directory
   - Add the following variables:
     ```
     NODE_ENV=development
     PORT=5005
     MONGODB_URI=your_mongodb_connection_string
     JWT_SECRET=your_jwt_secret
     JWT_EXPIRES_IN=30d
     CORS_ORIGIN=http://localhost:5173
     ```

4. Start the development server:
   ```bash
   # Start frontend and backend concurrently
   npm run dev:all

   # Or start them separately
   npm run dev        # Frontend
   npm run server     # Backend
   ```

5. Create admin user:
   ```bash
   npm run create-admin
   ```

6. Create database indexes:
   ```bash
   npm run create-indexes
   ```

## API Documentation

The API is documented using Swagger. When the server is running, you can access the Swagger UI at:

```
/api-docs
```

### API Structure

The API follows RESTful principles and is organized into the following resources:

- `/api/auth` - Authentication endpoints
- `/api/products` - Product management
- `/api/search` - Search functionality
- `/api/categories` - Category management
- `/api/admin` - Admin-only endpoints
- `/api/image-proxy` - Image optimization

## Frontend Development

### Component Structure

The frontend uses a component-based architecture with:
- Reusable UI components in `src/components/ui`
- Page components in `src/pages`
- Layout components for page structure

### State Management

- React's built-in useState and useContext for local and shared state
- Custom hooks for reusable logic

### Routing

React Router v6 is used for routing. Routes are defined in `App.tsx`.

### API Integration

API services are defined in `src/services/newApi.ts` with functions for each endpoint.

### Error Handling

- Global error boundary in `ErrorBoundary.tsx`
- Centralized error handling in `errorHandler.ts`
- Toast notifications for user feedback

### Performance Optimization

- Code splitting with React.lazy and Suspense
- Image optimization with the OptimizedImage component
- Client-side caching of API responses

## Backend Development

### Express Configuration

The Express application is configured in `server/server.js` with middleware for:
- CORS
- JSON parsing
- Security (Helmet, CSRF, etc.)
- Rate limiting
- Error handling

### Route Structure

Routes are defined in `server/routes` with controllers in `server/controllers`.

### Authentication

JWT-based authentication with:
- Login endpoint at `/api/auth/login`
- Protected routes using the `protect` middleware
- Role-based authorization with the `authorize` middleware

### Middleware

Custom middleware includes:
- Authentication (`auth.js`)
- Error handling (`errorHandler.js`)
- Security (`security.js`)
- Caching (`cache.js`)
- Image optimization (`imageOptimizer.js`)

## Database

### MongoDB Schema

The database uses Mongoose for schema definition and validation:
- `Product.js` - Product schema
- `User.js` - User schema

### Indexes

MongoDB indexes are defined for performance optimization:
- Text index on product name, brand, category, and description
- Indexes for common query fields (category, brand, score)
- Compound indexes for common query patterns

## Caching

### Server-Side Caching

Node-cache is used for server-side caching:
- Product data is cached for 10 minutes
- Search results are cached for 5 minutes
- Search suggestions are cached for 2 minutes

### Client-Side Caching

The frontend implements caching for:
- Product details
- Search results
- Categories

### Cache Invalidation

Cache is automatically invalidated when:
- A product is created, updated, or deleted
- The server is restarted

## Security

### Authentication

- JWT-based authentication
- Password hashing with bcrypt
- Token expiration after 7 days

### CSRF Protection

- CSRF tokens for all state-changing operations
- Token validation on the server

### Content Security Policy

- Helmet.js for CSP headers
- Restricted resource loading

### Input Validation

- Express Validator for request validation
- MongoDB query sanitization
- XSS protection

## Performance Optimization

### Backend Optimization

- MongoDB indexes for faster queries
- API response caching
- Efficient database queries

### Frontend Optimization

- Code splitting
- Lazy loading
- Image optimization
- Skeleton loaders for better UX

## Testing

### API Testing

Use Postman for API testing:
1. Import the Swagger documentation
2. Create environment variables for base URL and tokens
3. Run tests against endpoints

### Frontend Testing

Currently, there are no automated tests for the frontend.

## Deployment

### Production Setup

1. Set environment variables for production:
   ```
   NODE_ENV=production
   PORT=10000
   MONGODB_URI=your_production_mongodb_uri
   JWT_SECRET=your_production_jwt_secret
   JWT_EXPIRES_IN=30d
   ```

2. Build the frontend:
   ```bash
   npm run build
   ```

3. Start the production server:
   ```bash
   npm start
   ```

## Contribution Guidelines

### Code Style

- Use ESLint and Prettier for code formatting
- Follow the existing code style
- Use meaningful variable and function names

### Git Workflow

1. Create a feature branch from `main`
2. Make changes and commit with descriptive messages
3. Push to the remote repository
4. Create a pull request to `main`

### Pull Request Process

1. Ensure all tests pass
2. Update documentation if necessary
3. Get at least one code review
4. Merge after approval

---

For any questions or issues, please contact the project maintainers.
