# Production Deployment Guide

This guide covers the steps needed to deploy Halo to production safely and efficiently.

## Pre-Deployment Checklist

### Code Quality
- [ ] All console.log statements removed from production code
- [ ] TypeScript compilation passes without errors
- [ ] ESLint passes without warnings
- [ ] All tests pass (if applicable)
- [ ] Code has been reviewed and approved

### Environment Configuration
- [ ] Production environment variables are set
- [ ] Database connection string is configured
- [ ] API endpoints are configured for production
- [ ] CORS settings are properly configured
- [ ] Security headers are enabled

### Performance Optimization
- [ ] Bundle size has been analyzed and optimized
- [ ] Images are optimized and compressed
- [ ] Lazy loading is implemented where appropriate
- [ ] Caching strategies are in place
- [ ] CDN is configured (if applicable)

### Security
- [ ] All sensitive data is properly secured
- [ ] Authentication and authorization are working
- [ ] HTTPS is enabled
- [ ] Security headers are configured
- [ ] Input validation is implemented
- [ ] Rate limiting is configured

## Build Process

### 1. Clean Build
```bash
npm run clean
npm install
```

### 2. Type Check
```bash
npm run type-check
```

### 3. Lint Code
```bash
npm run lint
```

### 4. Production Build
```bash
npm run build:prod
```

### 5. Test Build Locally
```bash
npm run preview
```

## Environment Variables

### Required Production Variables
```env
NODE_ENV=production
MONGODB_URI=your_production_mongodb_uri
JWT_SECRET=your_secure_jwt_secret
PORT=5005
```

### Optional Variables
```env
VITE_API_BASE_URL=https://your-domain.com/api
VITE_BASE_PATH=/
```

## Deployment Steps

### For Render.com Deployment

1. **Connect Repository**
   - Connect your GitHub repository to Render
   - Select the main/production branch

2. **Configure Build Settings**
   - Build Command: `npm run render-postbuild`
   - Start Command: `npm start`
   - Node Version: 18 or higher

3. **Set Environment Variables**
   - Add all required environment variables in Render dashboard
   - Ensure `NODE_ENV=production`

4. **Deploy**
   - Trigger deployment from Render dashboard
   - Monitor build logs for any errors

### For Other Platforms

1. **Build the Application**
   ```bash
   npm run build:prod
   ```

2. **Install Production Dependencies**
   ```bash
   npm ci --production
   ```

3. **Start the Server**
   ```bash
   NODE_ENV=production node server/server.js
   ```

## Post-Deployment Verification

### Functional Testing
- [ ] Homepage loads correctly
- [ ] Search functionality works
- [ ] Product pages display properly
- [ ] Admin panel is accessible (if applicable)
- [ ] All navigation links work
- [ ] Mobile responsiveness is maintained

### Performance Testing
- [ ] Page load times are acceptable (< 3 seconds)
- [ ] API response times are reasonable
- [ ] Images load properly
- [ ] No JavaScript errors in console

### Security Testing
- [ ] HTTPS is working
- [ ] Authentication is secure
- [ ] No sensitive data exposed in client
- [ ] CORS is properly configured

## Monitoring and Maintenance

### Health Checks
- Monitor server uptime and response times
- Check database connectivity
- Monitor error rates and logs

### Performance Monitoring
- Track page load times
- Monitor bundle sizes
- Check Core Web Vitals

### Error Tracking
- Set up error tracking service (e.g., Sentry)
- Monitor and fix production errors
- Set up alerts for critical issues

## Rollback Plan

If issues are discovered after deployment:

1. **Immediate Rollback**
   - Revert to previous working version
   - Restore database if necessary

2. **Fix and Redeploy**
   - Identify and fix the issue
   - Test thoroughly in staging
   - Deploy the fix

## Common Issues and Solutions

### Build Failures
- Check TypeScript errors
- Verify all dependencies are installed
- Check for missing environment variables

### Runtime Errors
- Check server logs
- Verify database connectivity
- Check API endpoint configurations

### Performance Issues
- Analyze bundle size
- Check for memory leaks
- Optimize database queries

## Maintenance Schedule

### Daily
- Monitor error logs
- Check system health

### Weekly
- Review performance metrics
- Update dependencies (if needed)
- Backup database

### Monthly
- Security audit
- Performance optimization review
- Dependency updates

## Support and Troubleshooting

For deployment issues:
1. Check the deployment logs
2. Verify environment variables
3. Test locally with production build
4. Check database connectivity
5. Review security settings

Remember to always test thoroughly in a staging environment before deploying to production.
