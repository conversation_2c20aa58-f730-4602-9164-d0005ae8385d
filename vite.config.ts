import path from "path";
import { defineConfig, loadEnv } from "vite";
import react from "@vitejs/plugin-react-swc";

// https://vitejs.dev/config/
export default defineConfig(({ mode }) => {
  // Load env file based on `mode` in the current directory.
  // Set the third parameter to '' to load all env regardless of the `VITE_` prefix.
  const env = loadEnv(mode, process.cwd(), '');
  const isProduction = mode === 'production';

  return {
    base: mode === "development" ? "/" : env.VITE_BASE_PATH || "/",

    // Build optimizations
    build: {
      // Enable source maps in development only
      sourcemap: !isProduction,

      // Optimize bundle size
      rollupOptions: {
        output: {
          // Manual chunk splitting for better caching
          manualChunks: {
            vendor: ['react', 'react-dom', 'react-router-dom'],
            ui: ['lucide-react', '@radix-ui/react-dialog', '@radix-ui/react-popover'],
            utils: ['clsx', 'tailwind-merge']
          }
        }
      },

      // Minification settings
      minify: isProduction ? 'terser' : false,
      terserOptions: isProduction ? {
        compress: {
          drop_console: true, // Remove console.log in production
          drop_debugger: true
        }
      } : undefined,

      // Target modern browsers for better optimization
      target: 'es2020'
    },

    optimizeDeps: {
      entries: ["src/main.tsx"],
      include: ['react', 'react-dom', 'react-router-dom']
    },

    plugins: [
      react(),
    ],

    resolve: {
      preserveSymlinks: true,
      alias: {
        "@": path.resolve(__dirname, "./src"),
      },
    },

    server: {
      // @ts-ignore
      allowedHosts: true,
      host: '0.0.0.0',
      port: 5173,
      strictPort: true,
      proxy: {
        '/api': {
          target: 'http://localhost:5005',
          changeOrigin: true,
          secure: false,
          rewrite: (path) => path
        }
      }
    },

    // Preview server configuration (for production builds)
    preview: {
      port: 4173,
      host: '0.0.0.0'
    }
  };
});
