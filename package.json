{"name": "halocritique-opinions", "private": true, "version": "1.0.0", "scripts": {"dev": "npx vite", "build": "npx tsc --noEmit && npx vite build", "build:prod": "NODE_ENV=production npx tsc --noEmit && npx vite build", "build-no-errors": "npx tsc --noEmit || npx vite build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "lint:fix": "eslint . --ext ts,tsx --fix", "type-check": "npx tsc --noEmit", "preview": "npx vite preview", "server": "nodemon server/server.js", "server:prod": "cd server && npm install --production=true && cd .. && NODE_ENV=production node server/server.js", "dev:all": "concurrently \"npm run dev\" \"npm run server\"", "create-admin": "node server/utils/createAdminUser.js", "create-indexes": "node server/utils/createIndexes.js", "start": "npm run server:prod", "render-postbuild": "npm run build-no-errors", "clean": "rm -rf dist node_modules/.vite", "analyze": "npx vite-bundle-analyzer", "postinstall": "if [ -d \"server\" ]; then cd server && npm install --production=true; fi"}, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@hookform/resolvers": "^3.6.0", "@mui/material": "^7.0.2", "@radix-ui/react-accordion": "^1.1.2", "@radix-ui/react-alert-dialog": "^1.0.5", "@radix-ui/react-aspect-ratio": "^1.0.3", "@radix-ui/react-avatar": "^1.0.4", "@radix-ui/react-checkbox": "^1.0.4", "@radix-ui/react-collapsible": "^1.0.3", "@radix-ui/react-context-menu": "^2.1.5", "@radix-ui/react-dialog": "^1.1.11", "@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-hover-card": "^1.0.7", "@radix-ui/react-icons": "^1.3.0", "@radix-ui/react-label": "^2.0.2", "@radix-ui/react-menubar": "^1.0.4", "@radix-ui/react-navigation-menu": "^1.1.4", "@radix-ui/react-popover": "^1.0.7", "@radix-ui/react-progress": "^1.0.3", "@radix-ui/react-radio-group": "^1.1.3", "@radix-ui/react-scroll-area": "^1.0.5", "@radix-ui/react-select": "^2.0.0", "@radix-ui/react-separator": "^1.0.3", "@radix-ui/react-slider": "^1.1.2", "@radix-ui/react-slot": "^1.0.2", "@radix-ui/react-switch": "^1.0.3", "@radix-ui/react-tabs": "^1.0.4", "@radix-ui/react-toast": "^1.1.5", "@radix-ui/react-toggle": "^1.0.3", "@radix-ui/react-tooltip": "^1.0.7", "@supabase/supabase-js": "^2.45.6", "@vitejs/plugin-react-swc": "^3.8.1", "autoprefixer": "^10.4.19", "axios": "^1.8.4", "bcryptjs": "^2.4.3", "cheerio": "^1.0.0", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "cmdk": "^1.0.0", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "date-fns": "^3.6.0", "dotenv": "^16.3.1", "embla-carousel-react": "^8.1.5", "express": "^4.18.2", "express-mongo-sanitize": "^2.2.0", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "framer-motion": "^11.18.0", "helmet": "^8.1.0", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.394.0", "mongoose": "^8.0.0", "morgan": "^1.10.0", "natural": "^8.0.1", "node-cache": "^5.1.2", "node-cron": "^3.0.3", "postcss": "^8.4.38", "puppeteer": "^24.6.1", "react": "^18.2.0", "react-day-picker": "^8.10.1", "react-dom": "^18.2.0", "react-helmet-async": "^2.0.5", "react-hook-form": "^7.51.5", "react-resizable-panels": "^2.0.19", "react-router": "^6.23.1", "react-router-dom": "^6.23.1", "slugify": "^1.6.6", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.0", "tailwind-merge": "^2.3.0", "tailwindcss": "3.4.1", "tailwindcss-animate": "^1.0.7", "typescript": "^5.8.2", "uuid": "^9.0.1", "vaul": "^0.9.1", "vite": "^6.2.3", "winston": "^3.17.0", "xss-clean": "^0.1.4", "zod": "^3.23.8"}, "devDependencies": {"@types/node": "^20.14.2", "@types/react": "^18.2.66", "@types/react-dom": "^18.2.22", "@types/uuid": "^9.0.8", "concurrently": "^9.1.2", "nodemon": "^3.0.1", "tempo-devtools": "^2.0.99", "terser": "^5.43.1"}}